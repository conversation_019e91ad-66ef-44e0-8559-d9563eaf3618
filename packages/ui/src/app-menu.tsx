import { DropdownMenu } from "@radix-ui/themes";
import { PropsWithChildren, ReactNode } from "react";

export type AppMenuItemProps = {
  key: string;
  label: ReactNode;
  onClick?: () => void;
  color?: DropdownMenu.ItemProps["color"];
};

export const AppMenu = ({
  children,
  config,
}: PropsWithChildren<{
  config: AppMenuItemProps[];
}>) => {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>{children}</DropdownMenu.Trigger>
      <DropdownMenu.Content side="bottom" align="end">
        {config.map(({ key, label, onClick, color }) => {
          return (
            <DropdownMenu.Item key={key} onClick={onClick} color={color}>
              {label}
            </DropdownMenu.Item>
          );
        })}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
