import { Spinner, SpinnerProps } from "@radix-ui/themes";

interface LoadingPlaceholderProps {
  size?: SpinnerProps["size"];
  className?: string;
}

export const LoadingPlaceholder = ({
  size = "3",
  className = "",
}: LoadingPlaceholderProps) => {
  return (
    <div className={`flex items-center justify-center py-20 ${className}`}>
      <div className="flex items-center space-x-2">
        <Spinner size={size} />
      </div>
    </div>
  );
};
