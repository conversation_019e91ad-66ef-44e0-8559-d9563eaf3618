import { DropdownMenu } from "@radix-ui/themes";
import { PropsWithChildren, ReactNode } from "react";

export interface NavConfig {
  item: ReactNode;
  label: string;
}

export const NavMenu = (
  props: PropsWithChildren<{
    linkConfig: NavConfig[];
    action?: React.ReactNode;
  }>,
) => {
  const { linkConfig, children, action } = props;
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>{children}</DropdownMenu.Trigger>
      <DropdownMenu.Content side="bottom" align="end">
        {linkConfig.map(({ item, label }) => {
          return <DropdownMenu.Item key={label}>{item}</DropdownMenu.Item>;
        })}
        {action}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
