import { Button, ButtonProps } from "@radix-ui/themes";

export const PrimaryButton = (props: ButtonProps) => {
  return (
    <Button
      radius="full"
      size="3"
      variant="solid"
      color="orange"
      {...props}
    ></Button>
  );
};

export const SecondaryButton = (props: ButtonProps) => {
  return (
    <div className="flex flex-col px-3 py-2">
      <Button
        radius="full"
        size="3"
        variant="ghost"
        color="gray"
        {...props}
      ></Button>
    </div>
  );
};
