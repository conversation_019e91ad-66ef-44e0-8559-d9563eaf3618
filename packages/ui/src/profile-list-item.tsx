import { Separator, Text } from "@radix-ui/themes";
import { ReactNode } from "react";

export const ProfileListItem = ({
  label,
  value,
}: {
  label: string;
  value: ReactNode;
}) => {
  return (
    <>
      <div className="flex justify-between">
        <Text size="2" color="gray">
          {label}
        </Text>
        <Text size="3">{value}</Text>
      </div>
      {<Separator size="4" className="my-4 last:hidden" />}
    </>
  );
};
