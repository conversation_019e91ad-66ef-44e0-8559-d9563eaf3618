import { Text, TextProps } from "@radix-ui/themes";

export const AmountDisplay = ({
  amount,
  size,
  weight = "medium",
  showSign = true,
  dimmed = false,
}: {
  amount: number | undefined;
  size?: TextProps["size"];
  weight?: TextProps["weight"];
  showSign?: boolean;
  dimmed?: boolean;
}) => {
  if (amount == undefined) return "-";

  const textProps = { size, weight };

  if (amount > 0) {
    return (
      <Text color={dimmed ? "gray" : "green"} {...textProps}>
        {showSign ? "+" : ""}
        {amount.toLocaleString()}
      </Text>
    );
  }

  if (amount < 0) {
    return (
      <Text color={dimmed ? "gray" : "red"} {...textProps}>
        {showSign ? "-" : ""}
        {amount.toLocaleString()}
      </Text>
    );
  }

  return (
    <Text color="gray" {...textProps}>
      {amount.toLocaleString()}
    </Text>
  );
};
