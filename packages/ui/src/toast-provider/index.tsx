"use client";
import { Toast } from "radix-ui";
import { Callout } from "@radix-ui/themes";
import { PropsWithChildren, useEffect, useState } from "react";

type ToastType = "success" | "error" | "warning" | "info";

export class ToastInstance {
  show: (message: string, type: ToastType) => void = () => {};
}

const getMessageColor = (type: ToastType) => {
  switch (type) {
    case "success":
      return "green";
    case "error":
      return "red";
    case "warning":
      return "yellow";
    case "info":
      return "blue";
  }
};

export const ToastProvider = ({
  children,
  instance,
}: PropsWithChildren<{ instance: ToastInstance }>) => {
  const [messages, setMessages] = useState<
    {
      id: string;
      message: string;
      type: ToastType;
    }[]
  >([]);

  const createMessage = (message: string, type: ToastType) => {
    const id = crypto.randomUUID();
    setMessages((prev) => [...prev, { id, message, type }].slice(0, 3));
  };

  useEffect(() => {
    instance.show = createMessage;
  }, [instance]);

  useEffect(() => {
    if (!messages.length) return;
    const timeout = setTimeout(() => {
      setMessages((prev) => prev.slice(1));
    }, 2000);

    return () => {
      clearTimeout(timeout);
    };
  }, [messages]);

  return (
    <Toast.Provider>
      <Toast.Root open={!!messages.length} className="flex flex-col gap-2">
        {messages.map(({ id, message, type }) => {
          return (
            <Callout.Root
              key={id}
              size="1"
              color={getMessageColor(type)}
              className="shadow-sm"
              variant="soft"
            >
              <Callout.Text>{message}</Callout.Text>
            </Callout.Root>
          );
        })}
      </Toast.Root>

      {children}
      <Toast.Viewport className="w-full fixed top-4 md:top-2 left-0 w-auto z-50 flex flex-col items-center gap-2 pointer-events-none" />
    </Toast.Provider>
  );
};
