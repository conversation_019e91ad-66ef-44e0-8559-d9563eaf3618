@use "tailwindcss" as *;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-roboto-sans);
  --font-mono: var(--font-roboto-mono);
}

// @media (prefers-color-scheme: dark) {
//   :root {
//     --background: #0a0a0a;
//     --foreground: #ededed;
//   }
// }

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

.radix-themes {
  --default-font-family: var(--font-roboto-sans);
  --default-mono-font-family: var(--font-roboto-mono);
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.section-content {
  @apply col-span-full lg:col-start-2 lg:col-span-10;
}

.section-content-2 {
  @apply col-span-full md:col-start-2 md:col-span-10;
}
