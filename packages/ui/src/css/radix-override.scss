body,
.light,
.light-theme {
  --orange-1: #fefcfb;
  --orange-2: #fff7ed;
  --orange-3: #ffefd6;
  --orange-4: #ffdfb5;
  --orange-5: #ffd19a;
  --orange-6: #ffc182;
  --orange-7: #f5ae73;
  --orange-8: #ec9455;
  --orange-9: #f76b15;
  --orange-10: #ef5f00;
  --orange-11: #cc4e00;
  --orange-12: #cc4e00;
  // --orange-12: #582d1d;

  --orange-a1: #c0400004;
  --orange-a2: #ff55000f;
  --orange-a3: #ff620022;
  --orange-a4: #ff5e003f;
  --orange-a5: #ff5d0053;
  --orange-a6: #ff5a016a;
  --orange-a7: #f94e007d;
  --orange-a8: #e84a009c;
  --orange-a9: #f65e00ea;
  --orange-a10: #eb5d00;
  --orange-a11: #b65605;
  --orange-a12: #b65605;

  // --orange-a1: #17120e;
  // --orange-a2: #1e160f;
  // --orange-a3: #331e0b;
  // --orange-a4: #462100;
  // --orange-a5: #562800;
  // --orange-a6: #66350c;
  // --orange-a7: #7e451d;
  // --orange-a8: #a35829;
  // --orange-a9: #f76b15;
  // --orange-a10: #ff801f;
  // --orange-a11: #ffa057;
  // --orange-a12: #ffe0c2;

  --orange-contrast: #fff;
  --orange-surface: #fff3eccc;
  --orange-indicator: #f76b15;
  --orange-track: #f76b15;

  .radix-themes {
    --cursor-button: pointer;

    --font-size-1: calc(12px * var(--scaling));
    --font-size-2: calc(14px * var(--scaling));
    --font-size-3: calc(16px * var(--scaling));
    --font-size-4: calc(18px * var(--scaling));
    --font-size-5: calc(20px * var(--scaling));
    --font-size-6: calc(24px * var(--scaling));
    --font-size-7: calc(32px * var(--scaling));
    --font-size-8: calc(40px * var(--scaling));
    --font-size-9: calc(60px * var(--scaling));

    --line-height-1: calc(16px * var(--scaling));
    --line-height-2: calc(20px * var(--scaling));
    --line-height-3: calc(24px * var(--scaling));
    --line-height-4: calc(26px * var(--scaling));
    --line-height-5: calc(28px * var(--scaling));
    --line-height-6: calc(30px * var(--scaling));
    --line-height-7: calc(40px * var(--scaling));
    --line-height-8: calc(46px * var(--scaling));
    --line-height-9: calc(60px * var(--scaling));
  }
}
