import { Text } from "@radix-ui/themes";
import { ReactNode } from "react";

export interface DetailRowProps {
  label: string;
  value: string | number | ReactNode;
  enableCopy?: boolean;
  copyText?: string;
  variant?: "horizontal" | "column";
  labelSize?: "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9";
  valueSize?: "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8" | "9";
  CopyComponent?: React.ComponentType<{ text: string }>;
}

export const DetailRow = ({
  label,
  value,
  enableCopy,
  copyText,
  variant = "column",
  labelSize = "2",
  valueSize = "2",
  CopyComponent,
}: DetailRowProps) => {
  if (variant === "horizontal") {
    return (
      <div className="flex justify-between">
        <Text size={labelSize} color="gray">
          {label}
        </Text>
        <div className="flex gap-2 max-w-[50%]">
          <Text size={valueSize} className="font-medium text-right break-all">
            {value}
          </Text>
          {enableCopy && copyText && CopyComponent && (
            <CopyComponent text={copyText} />
          )}
        </div>
      </div>
    );
  }

  // Column variant (default)
  return (
    <div className="flex flex-col gap-1 items-start">
      <Text size={labelSize} color="gray">
        {label}
      </Text>
      <div className="flex gap-2 items-center min-w-0 flex-1 justify-end">
        <Text size={valueSize}>{value}</Text>
        {enableCopy && copyText && CopyComponent && (
          <CopyComponent text={copyText} />
        )}
      </div>
    </div>
  );
};
