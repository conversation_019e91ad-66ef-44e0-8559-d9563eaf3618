---
type: "always_apply"
---

# Piccolo Frontend 開發規範 (Agent Rules)

本文件旨在為 AI Agent 提供操作此程式碼庫時應遵循的規則與慣例，以確保所有修改都符合專案的既有風格與架構。

## 總體原則

1.  **觀察與模仿**：在進行任何修改前，優先分析相關檔案與目錄的既有程式碼，並嚴格模仿其風格、命名和結構。
2.  **遵守 Monorepo 結構**：明確區分 `apps` 和 `packages` 的用途，不要在它們之間錯誤地移動或建立程式碼。
3.  **保持 DRY (Don't Repeat Yourself)**：如果發現多個應用程式中存在重複的邏輯或 UI 組件，應考慮將其提取到 `packages/` 目錄下的共享套件中。

## 技術棧與工具

- **框架**: Next.js (App Router)
- **語言**: TypeScript
- **狀態管理**: TanStack Query (React Query) 用於伺服器狀態管理。
- **樣式**: CSS Modules with Sass (`.module.scss`)
- **程式碼品質**: ESLint & Prettier

## 編碼風格與慣例

### 1. 命名慣例 (Naming Conventions)

- **目錄與非組件檔案**: 統一使用 `kebab-case` (小寫並用連字號分隔)。例如：`user-management`, `use-screen.ts`。
- **React 組件檔案**: 統一使用 `kebab-case` (例如 `user-menu.tsx`) 或將組件放在同名 `kebab-case` 目錄下的 `index.tsx` 中 (例如 `user-menu/index.tsx`)。組件本身的 class/function 命名維持 `PascalCase` (例如 `UserMenu`)。
- **Hook**: 統一使用 `camelCase`，並以 `use` 開頭。例如：`useAuth`, `useScreen`。

### 2. 組件開發 (Component Development)

- **共享組件**: 應建立在 `packages/ui/src/` 目錄下。這些是可在 `admin-web` 和 `client-web` 之間共享的通用組件。
- **應用專屬組件**: 應建立在 `apps/<app-name>/components/` 目錄下。這些組件與特定應用的業務邏輯緊密相關。
- **樣式**: 組件樣式必須使用 `.module.scss` 檔案，以確保樣式隔離。

### 3. API 層 (API Layer)

- 位於 `apps/*/api/` 目錄下的檔案 (`Api.ts`, `data-contracts.ts`, `http-client.ts`) 是 **自動生成** 的。
- **絕對不要** 手動修改這些檔案。如果需要更新 API，應當透過更新 API spec (例如 OpenAPI/Swagger) 並重新生成客戶端程式碼來完成。

### 4. 狀態管理 (State Management)

- 所有與後端數據的交互（查詢、新增、修改、刪除）都應優先使用 `TanStack Query`。
- 在 `app/query-provider.tsx` 中查看現有的 Query Client 設定。

### 5. 國際化 (i18n)

- 在 `apps/client-web` 中，所有面向使用者的靜態字串 **必須** 使用 i18n 機制處理。
- 英文字串定義在 `messages/en.json`，中文字串定義在 `messages/zh.json`。
- 新增或修改字串時，需同時更新所有語言檔案。

### 6. UI 庫使用 (UI Library Usage)

- **主要 UI 庫**: 本專案使用 `@radix-ui/themes` 作為基礎組件庫。所有新開發的 UI 組件應優先基於 Radix Themes 提供的組件進行封裝或擴充。
- **圖示**: 使用 `@radix-ui/react-icons` 來提供圖示。
- **樣式客製化**: 可以使用 `Tailwind CSS` 的 utility classes 來對 Radix 組件進行微調或建立客製化樣式。相關的樣式覆寫可以在 `packages/ui/src/css/radix-override.scss` 中找到。

### 8. 工具鏈 (Tooling)

- 在提交任何程式碼變更前，應確保執行過相關的 lint 和 format 指令，以符合專案的程式碼品質要求。
- 遵循 `.prettierrc` 和 `eslint.config.js` 中定義的規則。
