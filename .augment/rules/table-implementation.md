---
type: "agent_requested"
description: "Table Implementation"
---

### 表單實作 (Table Implementation)

當需要實作數據表格時，應遵循以下基於 `TanStack Table` 和 `Radix UI` 的模式：

1.  **核心技術**:

    - **邏輯層**: 使用 `@tanstack/react-table` 作為 Headless UI 函式庫來處理所有表格的狀態、排序、篩選等邏輯。
    - **表現層**: 使用 `@radix-ui/themes` 的 `<Table.*>` 組件來渲染表格的 UI。

2.  **結構分離 (Separation of Concerns)**:

    - **欄位定義 (Column Definitions)**: 表格的欄位設定 **必須** 抽離到一個獨立的 Custom Hook 中，並以 `use...Columns` 命名 (例如 `useTransactionColumns`)。這個 Hook 負責定義 `columns` 陣列，包括 `accessorKey`, `header` 以及 `cell` 的渲染方式。
    - **互動操作**: 如果欄位包含操作按鈕 (例如「查看詳情」)，應將事件處理函式 (event handler) 作為參數傳入這個 Hook，並在 `cell` 的渲染函式中呼叫。
    - **主組件 (Table Component)**: 表格的主組件 (例如 `TransactionTable`) 負責：
      - 接收 `data` 陣列。
      - 呼叫 `use...Columns` Hook 來獲取欄位定義。
      - 使用 `useReactTable` Hook 來初始化表格實例 (instance)。
      - 使用 `flexRender` 搭配 Radix 組件 (`<Table.Header>`, `<Table.Body>`, etc.) 來渲染 UI。

3.  **空狀態處理 (Empty State)**:

    - **必須** 檢查傳入的 `data` 陣列是否為空。如果為空，應渲染一個提示使用者沒有資料的「空狀態」畫面。
    - 優先使用共享的 `InfoLayout` 組件 (`@/ui-components/info-layout`) 來確保空狀態的 UI 一致性。

4.  **樣式與類型**:
    - **樣式**: 組件的特定樣式應寫在同層目錄的 `index.module.scss` 中。如果有效能釘選 (pinning) 等通用表格樣式，應建立一個共享的 utility 函式 (例如 `getCommonPinningStyles`)。
    - **類型**: 所有傳入的 `data` 和欄位定義都應使用從 `@/api/data-contracts` 引入的 DTO 進行嚴格的類型定義。
