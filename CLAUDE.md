# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Turborepo monorepo containing two Next.js applications:
1. `admin-web` - Admin portal running on port 3001
2. `client-web` - Client portal running on port 3000

The repository also includes shared packages:
- `@repo/ui` - Shared UI components
- `@repo/eslint-config` - Shared ESLint configuration
- `@repo/typescript-config` - Shared TypeScript configuration

## Development Commands

### Root-level commands (run from root directory):
```bash
# Install dependencies
npm install

# Run both applications in development mode
npm run dev

# Build all applications and packages
npm run build

# Lint all applications and packages
npm run lint

# Format code with Prettier
npm run format

# Check TypeScript types across all packages
npm run check-types

# Generate API clients from Swagger definitions
npm run api:gen
```

### App-specific commands (run from app directory):
```bash
# Run admin web application
cd apps/admin-web && npm run dev

# Run client web application
cd apps/client-web && npm run dev

# Build a specific app
cd apps/[app-name] && npm run build

# Lint a specific app
cd apps/[app-name] && npm run lint

# Generate API client for a specific app
cd apps/[app-name] && npm run api:gen
```

## Project Structure

```
├── apps/
│   ├── admin-web/          # Admin portal application
│   └── client-web/         # Client portal application
├── packages/
│   ├── ui/                 # Shared UI components
│   ├── eslint-config/      # Shared ESLint configuration
│   └── typescript-config/  # Shared TypeScript configuration
├── turbo.json              # Turborepo configuration
└── package.json            # Root package.json with workspace definitions
```

## Key Features

1. **Turborepo**: Used for managing the monorepo, caching builds, and running tasks efficiently
2. **Next.js 15**: Both applications use the latest Next.js with Turbopack
3. **TypeScript**: Full TypeScript support across all packages
4. **Shared Components**: UI components are shared through the `@repo/ui` package
5. **API Generation**: Uses `swagger-typescript-api` to generate TypeScript API clients from Swagger definitions
6. **Internationalization**: Client-web includes next-intl for i18n support

## Development Workflow

1. Run `npm install` from the root to install all dependencies
2. Use `npm run dev` to start both applications simultaneously
3. Access admin portal at http://localhost:3001
4. Access client portal at http://localhost:3000
5. For app-specific development, navigate to the app directory and run its dev script

## Testing

Tests are run using the Turborepo task runner:
```bash
# Run tests for all packages/apps
npm run test

# Run tests for a specific app
cd apps/[app-name] && npm run test
```