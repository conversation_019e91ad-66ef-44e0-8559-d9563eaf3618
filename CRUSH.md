# Pi<PERSON>lo Frontend - CRUSH Guide

## Important!!
- read rules in `.augment/rules`

## Project Structure
- Turborepo monorepo with two Next.js apps:
  - `apps/admin-web` (Admin portal on port 3001)
  - `apps/client-web` (Client portal on port 3000)
- Shared packages in `packages/` directory

## Essential Commands

### Root-level commands (run from root directory):
```bash
# Install dependencies
npm install

# Run both applications in development mode
npm run dev

# Build all applications and packages
npm run build

# Lint all applications and packages
npm run lint

# Format code with Prettier
npm run format

# Check TypeScript types across all packages
npm run check-types

# Generate API clients from Swagger definitions
npm run api:gen
```

### App-specific commands (run from app directory):
```bash
# Run admin web application
cd apps/admin-web && npm run dev

# Run client web application
cd apps/client-web && npm run dev

# Run a single test file (example)
cd apps/client-web && npx jest path/to/test-file.test.ts

# Run tests matching a pattern (example)
cd apps/client-web && npx jest -t "dashboard"

# Lint a specific app
cd apps/[app-name] && npm run lint

# Check types in a specific app
cd apps/[app-name] && npm run check-types

# Generate API client for a specific app
cd apps/[app-name] && npm run api:gen
```

## Code Style Guidelines

### Imports
- Use absolute imports with `@/` alias for app-specific imports
- Use `@repo/ui` for shared UI components
- Use `@repo/eslint-config` and `@repo/typescript-config` for shared configs
- Group imports in order: external libraries, shared packages, app-specific

### Formatting
- Prettier with:
  - printWidth: 80
  - tabWidth: 2
  - singleQuote: false
  - trailingComma: "all"
  - bracketSpacing: true
  - arrowParens: "always"

### TypeScript
- Strict typing required
- Use interfaces for object shapes
- Use types for unions and primitives
- Prefer type inference where possible

### Naming Conventions
- PascalCase for components and interfaces
- camelCase for variables and functions
- UPPER_SNAKE_CASE for constants
- kebab-case for file names
- Use descriptive names; avoid abbreviations

### React Patterns
- Use functional components with hooks
- Use TanStack Query for data fetching
- Use TanStack Table for complex tables
- Prefer compound components for complex UI
- Use React Server Components where appropriate (Next.js App Router)

### Error Handling
- Use try/catch for async operations
- Create custom error types for specific cases
- Display user-friendly error messages
- Log errors appropriately for debugging

### Testing
- Use Jest for unit tests
- Place test files in `__tests__` directories or alongside components
- Test business logic and complex components
- Mock external dependencies