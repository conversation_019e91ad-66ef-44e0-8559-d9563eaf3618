/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { AuthCallbackRequest } from "./data-contracts";
import { HttpClient, RequestParams } from "./http-client";

export class V1<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags v-1-authentication-controller
   * @name PreLogin
   * @request GET:/v1/auth/pre-login
   */
  preLogin = (params: RequestParams = {}) =>
    this.request<any, void>({
      path: `/v1/auth/pre-login`,
      method: "GET",
      ...params,
    });
  /**
   * No description
   *
   * @tags v-1-authentication-controller
   * @name Callback
   * @request GET:/v1/auth/callback
   */
  callback = (
    query: {
      req: AuthCallbackRequest;
    },
    params: RequestParams = {},
  ) =>
    this.request<any, void>({
      path: `/v1/auth/callback`,
      method: "GET",
      query: query,
      ...params,
    });
}
