/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  KycOnboardingMultipartFormData,
  ResponseResultCryptoDepositInfoResponse,
  ResponseResultDashboardResponse,
  ResponseResultFiatDepositInfoResponse,
  ResponseResultKycVerificationStatus,
  ResponseResultListCountryDTO,
  ResponseResultListIdentityDocumentTypeDTO,
  ResponseResultObject,
  ResponseResultPagedInvestmentPayoutDTO,
  ResponseResultPagedTransactionDTO,
  ResponseResultTransactionDetailsResponse,
  ResponseResultUserDTO,
  ResponseResultVoid,
  SubmitCryptoDepositRequest,
  SubmitFiatDepositMultipartFormData,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Api<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags V1KycOnboardingController
   * @name Submit
   * @summary Submit KYC Application
   * @request POST:/api/v1/kyc/onboarding
   */
  submit = (data: KycOnboardingMultipartFormData, params: RequestParams = {}) =>
    this.request<
      ResponseResultVoid,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/onboarding`,
      method: "POST",
      body: data,
      type: ContentType.FormData,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DepositController
   * @name SubmitFiatDeposit
   * @summary Submit a FIAT deposit request
   * @request POST:/api/v1/deposits/submit/fiat
   */
  submitFiatDeposit = (
    data: SubmitFiatDepositMultipartFormData,
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultVoid,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/deposits/submit/fiat`,
      method: "POST",
      body: data,
      type: ContentType.FormData,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DepositController
   * @name SubmitCryptoDeposit
   * @request POST:/api/v1/deposits/submit/crypto
   */
  submitCryptoDeposit = (
    data: SubmitCryptoDepositRequest,
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultVoid,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/deposits/submit/crypto`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1UserController
   * @name GetUserProfile
   * @request GET:/api/v1/user/profile
   */
  getUserProfile = (params: RequestParams = {}) =>
    this.request<
      ResponseResultUserDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/user/profile`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1TransactionController
   * @name GetTransactions
   * @request GET:/api/v1/transactions
   */
  getTransactions = (
    query?: {
      /**
       * 1-indexed page number
       * @format int32
       * @min 1
       * @default 1
       */
      page?: number;
      /**
       * @format int32
       * @min 1
       * @max 100
       * @default 20
       */
      pageSize?: number;
      status?: "PENDING" | "FAILED" | "SUCCESSFUL";
    },
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultPagedTransactionDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/transactions`,
      method: "GET",
      query: query,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1TransactionController
   * @name GetTransactionDetails
   * @request GET:/api/v1/transactions/{transactionId}
   */
  getTransactionDetails = (transactionId: string, params: RequestParams = {}) =>
    this.request<
      ResponseResultTransactionDetailsResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/transactions/${transactionId}`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * @description Returns the current KYC verification status. Possible values: PENDING, APPROVED, REJECTED, RESTRICTED, or null (no verification submitted).
   *
   * @tags V1KycOnboardingController
   * @name VerificationStatus
   * @summary Get KYC verification status
   * @request GET:/api/v1/kyc/onboarding/status
   */
  verificationStatus = (params: RequestParams = {}) =>
    this.request<
      ResponseResultKycVerificationStatus,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/onboarding/status`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1KycOnboardingController
   * @name GetIdentityDocumentTypes
   * @request GET:/api/v1/kyc/onboarding/document/identity/types
   */
  getIdentityDocumentTypes = (params: RequestParams = {}) =>
    this.request<
      ResponseResultListIdentityDocumentTypeDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/onboarding/document/identity/types`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1KycOnboardingController
   * @name GetCountries
   * @request GET:/api/v1/kyc/onboarding/countries
   */
  getCountries = (params: RequestParams = {}) =>
    this.request<
      ResponseResultListCountryDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/kyc/onboarding/countries`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DepositController
   * @name GetFiatDepositInfo
   * @request GET:/api/v1/deposits/info/fiat
   */
  getFiatDepositInfo = (params: RequestParams = {}) =>
    this.request<
      ResponseResultFiatDepositInfoResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/deposits/info/fiat`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DepositController
   * @name GetCryptoDepositInfo
   * @request GET:/api/v1/deposits/info/crypto
   */
  getCryptoDepositInfo = (params: RequestParams = {}) =>
    this.request<
      ResponseResultCryptoDepositInfoResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/deposits/info/crypto`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DashboardController
   * @name GetDashboardInfo
   * @request GET:/api/v1/dashboard
   */
  getDashboardInfo = (params: RequestParams = {}) =>
    this.request<
      ResponseResultDashboardResponse,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/dashboard`,
      method: "GET",
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1DashboardController
   * @name GetInvestmentPayouts
   * @request GET:/api/v1/dashboard/payouts
   */
  getInvestmentPayouts = (
    query?: {
      /**
       * @format int32
       * @min 1
       * @default 1
       */
      page?: number;
    },
    params: RequestParams = {},
  ) =>
    this.request<
      ResponseResultPagedInvestmentPayoutDTO,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/dashboard/payouts`,
      method: "GET",
      query: query,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1AuthController
   * @name Logout
   * @request GET:/api/v1/auth/logout
   */
  logout = (params: RequestParams = {}) =>
    this.request<
      void,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/auth/logout`,
      method: "GET",
      ...params,
    });
  /**
   * No description
   *
   * @tags V1AuthController
   * @name Login
   * @request GET:/api/v1/auth/login
   */
  login = (params: RequestParams = {}) =>
    this.request<
      void,
      (ResponseResultVoid | ResponseResultObject) | ResponseResultObject
    >({
      path: `/api/v1/auth/login`,
      method: "GET",
      ...params,
    });
}
