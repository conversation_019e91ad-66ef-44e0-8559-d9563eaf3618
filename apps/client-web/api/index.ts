import Cookies from "js-cookie";
import { API_BASE_URL } from "@/app/constants";
import { Api } from "./Api";

export const customFetch: typeof fetch = (url, options) => {
  const method = options?.method ?? "";
  const customizeOption: RequestInit = { credentials: "include" };

  if (!["GET", "HEAD", "TRACE", "OPTIONS"].includes(method)) {
    customizeOption.headers = {
      ...options?.headers,
      "X-XSRF-TOKEN": Cookies.get("XSRF-TOKEN") ?? "",
    };
  }

  return fetch(url, { ...options, ...customizeOption });
};

const service = new Api({
  baseUrl: API_BASE_URL,
  customFetch,
});

export default service;
