import { Button, ButtonProps } from "@radix-ui/themes";

export const PrimaryButton = (props: ButtonProps) => {
  return (
    <Button
      radius="full"
      variant="solid"
      color="orange"
      highContrast
      {...props}
    ></Button>
  );
};

export const SecondaryButton = (props: ButtonProps) => {
  return (
    <Button radius="full" color="gray" variant="surface" {...props}></Button>
  );
};
