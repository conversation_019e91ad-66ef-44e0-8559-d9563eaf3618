"use client";
import { Link, usePathname } from "@/i18n/navigation";
import { TabNav } from "@radix-ui/themes";
import classNames from "classnames";
import Image from "next/image";
import style from "./index.module.scss";
import { renderLink, type LinkConfig } from "./utils";
import { useTranslations } from "next-intl";
import { LandingPageMenu } from "@/app/[locale]/(landingpage)/_components/landing-page-nav-actions";
import { useKycStatus } from "@/hooks/use-kyc-status";
import { useMemo } from "react";
import { useAuth } from "@/hooks/use-auth";
import { UserAvatar } from "../user-avatar";

export const AppNav = () => {
  const { isAuthed } = useAuth();
  const { isKycApproved } = useKycStatus();
  const pathname = usePathname();

  const t = useTranslations();

  const linkConfig: LinkConfig[] = useMemo(() => {
    const list = [
      {
        href: "/about",
        label: t("Nav.about"),
      },
      {
        href: "/invest",
        label: t("Nav.invest"),
      },
    ];

    if (isAuthed) {
      list.push(
        {
          href: isKycApproved ? "/dashboard" : "/kyc",
          label: t("Nav.dashboard"),
        },
        {
          href: isKycApproved ? "/transaction-history" : "/kyc",
          label: t("Nav.transactionHistory"),
        },
      );
    }

    return list;
  }, [isAuthed, isKycApproved, t]);

  const hidePageLinks = pathname === "/kyc" || pathname === "/restricted";

  return (
    <div className="flex gap-6 h-18 md:h-15 items-center justify-between">
      <div className="flex items-center gap-6">
        <Link href="/">
          <Image src="/odc-logo-bw.png" alt="Nav logo" width={65} height={32} />
        </Link>
        {hidePageLinks ? null : (
          <div className="hidden md:block">
            <TabNav.Root color="gray" className={classNames(style.nav)}>
              {linkConfig.map(({ href, label }) => {
                const active = pathname === href;

                return (
                  <TabNav.Link
                    key={label}
                    asChild
                    active={active}
                    className={style.tab}
                  >
                    <Link href={href}>
                      <span>{label}</span>
                    </Link>
                  </TabNav.Link>
                );
              })}
            </TabNav.Root>
          </div>
        )}
      </div>

      {hidePageLinks ? null : (
        <div>
          {isAuthed ? (
            <UserAvatar linkConfig={linkConfig} />
          ) : (
            <LandingPageMenu linkConfig={linkConfig.map(renderLink)} />
          )}
        </div>
      )}
    </div>
  );
};
