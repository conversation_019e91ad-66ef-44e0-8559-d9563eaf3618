import { Collapsible } from "radix-ui";
import style from "./index.module.scss";
import { ReactNode, useState } from "react";
import classNames from "classnames";
import { CaretDownIcon, CaretUpIcon } from "@radix-ui/react-icons";

export const CollapseItem = ({
  title,
  children,
  titleClassName,
}: {
  title: ReactNode;
  children: ReactNode;
  titleClassName?: string;
}) => {
  const [open, setOpen] = useState(false);
  return (
    <Collapsible.Root
      className="py-6 first:pt-0 last:pb-0 z-0"
      open={open}
      onOpenChange={setOpen}
    >
      <Collapsible.Trigger
        className={classNames(
          "w-full cursor-pointer flex justify-between items-center py-3 -my-3 relative z-10",
          titleClassName,
        )}
      >
        <div>{title}</div>
        <div>
          {open ? (
            <CaretUpIcon width={16} height={16} />
          ) : (
            <CaretDownIcon width={16} height={16} />
          )}
        </div>
      </Collapsible.Trigger>
      <Collapsible.Content
        className={classNames(
          style.collapseTransition,
          "overflow-hidden relative z-0",
        )}
      >
        {children}
      </Collapsible.Content>
    </Collapsible.Root>
  );
};
