import { CopyButton } from "@/ui-components/copy-button";
import { Heading, Text, TextField } from "@radix-ui/themes";
import classNames from "classnames";

type InputFieldProps<T extends object, K extends keyof T = keyof T> = {
  name?: Extract<K, K extends string ? K : never>;
  placeholder?: string;
  title: string;
  required?: boolean;
  disabled?: boolean;
  value?: string;
  remarks?: string;
  className?: string;
  enableCopy?: boolean;
  onChange?: (value: T[K], key?: K) => void;
};

export const InputField = <T extends object, K extends keyof T = keyof T>({
  name,
  placeholder,
  title,
  required,
  disabled,
  value,
  remarks,
  className,
  enableCopy,
  onChange,
}: InputFieldProps<T, K>) => {
  return (
    <div className={classNames("flex flex-col gap-2", className)}>
      <Heading as="h6" size="2">
        {title}
        {required ? "*" : ""}
      </Heading>
      <TextField.Root
        name={name}
        placeholder={placeholder}
        value={value}
        disabled={disabled}
        onChange={(event) => onChange?.(event.target.value as T[K], name)}
      >
        {enableCopy && (
          <TextField.Slot side="right">
            <CopyButton text={value ?? ""} />
          </TextField.Slot>
        )}
      </TextField.Root>
      <Text size="2" color="gray" className="opacity-50">
        {remarks}
      </Text>
    </div>
  );
};
