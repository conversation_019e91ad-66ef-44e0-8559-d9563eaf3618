import { Heading, Select } from "@radix-ui/themes";
import { useMemo } from "react";

export const SelectField = <T extends object, K extends keyof T = keyof T>({
  name,
  placeholder,
  title,
  required,
  options,
  value,
  onChange,
  disabled,
}: {
  name: Extract<K, K extends string ? K : never>;
  placeholder?: string;
  title: string;
  required?: boolean;
  options: { value: string; label: string }[];
  value?: string;
  onChange?: (value: T[K], key?: K) => void;
  disabled?: boolean;
}) => {
  const content = useMemo(() => {
    return options.map(({ value, label }) => (
      <Select.Item key={value} value={value}>
        {label}
      </Select.Item>
    ));
  }, [options]);
  return (
    <div className="flex flex-col gap-2">
      <Heading as="h6" size="2">
        {title}
        {required ? "*" : ""}
      </Heading>
      <Select.Root
        value={value}
        onValueChange={(value) => onChange?.(value as T[K], name)}
        disabled={disabled}
      >
        <Select.Trigger name={name} placeholder={placeholder} />
        <Select.Content>{content}</Select.Content>
      </Select.Root>
    </div>
  );
};
