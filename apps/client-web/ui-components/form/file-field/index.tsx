import { TrashIcon } from "@radix-ui/react-icons";
import { But<PERSON>, Heading, IconButton, Text } from "@radix-ui/themes";
import React from "react";

export const FileField = ({
  name,
  placeholder = "Select file",
  title,
  required,
  value,
  accept,
  onChange,
}: {
  name: string;
  placeholder?: string;
  title: string;
  required?: boolean;
  value?: File;
  accept?: string;
  onChange?: (value: File | undefined) => void;
}) => {
  const handleOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onChange?.(file);
    }
  };

  return (
    <div className="flex flex-col gap-2 items-start">
      <Heading as="h6" size="2">
        {title}
        {required ? "*" : ""}
      </Heading>
      <label>
        <input
          className="hidden"
          type="file"
          name={name}
          onChange={handleOnChange}
          accept={accept}
        />
        {value ? (
          <div className="flex gap-2 items-center min-h-8">
            <Text size="3" className="color-primary">
              {value.name}
            </Text>{" "}
            <IconButton
              variant="ghost"
              color="gray"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                onChange?.(undefined);
              }}
            >
              <TrashIcon width={20} height={20} />
            </IconButton>
          </div>
        ) : (
          <Button
            radius="full"
            variant="soft"
            color="gray"
            type="button"
            asChild
          >
            <span>{placeholder}</span>
          </Button>
        )}
      </label>
    </div>
  );
};
