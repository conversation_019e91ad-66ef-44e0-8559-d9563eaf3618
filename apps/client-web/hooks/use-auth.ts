import service from "@/api";
import { getErrorStatus } from "@/utils/error";
import { useQuery } from "@tanstack/react-query";

export const useAuth = ({
  requireAuth = false,
}: { requireAuth?: boolean } = {}) => {
  const { data } = useQuery({
    queryFn: async () => {
      try {
        const res = await service.getUserProfile();
        return res.data?.data;
      } catch (error) {
        if (requireAuth && getErrorStatus(error) === 401) {
          window.location.replace("/");
        }
        return null;
      }
    },
    queryKey: ["user"],
  });

  return {
    userData: data ?? {},
    isAuthed: data?.publicId !== undefined,
  };
};
