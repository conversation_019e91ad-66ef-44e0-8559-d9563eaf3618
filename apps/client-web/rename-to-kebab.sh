#!/bin/bash

# Script to rename files in public/graphics to kebab case

cd public/graphics/white

# Loop through all PNG files
for file in *.png; do
  # Skip if file doesn't exist
  [ -e "$file" ] || continue

  # Convert to kebab case
  # 1. Insert a hyphen before each uppercase letter
  # 2. Convert to lowercase
  # 3. Remove any leading hyphen
  kebab_name=$(echo "$file" | sed 's/\([A-Z]\)/-\1/g' | tr '[:upper:]' '[:lower:]' | sed 's/^-//')

  # Only rename if the name has changed
  if [ "$file" != "$kebab_name" ]; then
    echo "Renaming: $file -> $kebab_name"
    mv "$file" "$kebab_name"
  else
    echo "No change needed for: $file"
  fi
done

echo "Renaming complete!"
