import { useEffect, useRef, useState } from "react";

export const useContainerMargin = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerMargin, setContainerMargin] = useState(0);

  useEffect(() => {
    const updateContainerMargin = () => {
      if (containerRef.current) {
        const firstChild = containerRef.current.firstElementChild
          ?.firstElementChild as HTMLElement | null;

        const margin = firstChild?.getBoundingClientRect().left ?? 0;

        setContainerMargin(margin);
      }
    };

    updateContainerMargin();

    window.addEventListener("resize", updateContainerMargin);
    return () => {
      window.removeEventListener("resize", updateContainerMargin);
    };
  });

  return { containerMargin, containerRef };
};
