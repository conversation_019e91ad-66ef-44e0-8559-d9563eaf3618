import Link from "next/link";
import LandingPageLayout from "./[locale]/(landingpage)/layout";
import { NextIntlClientProvider, useTranslations } from "next-intl";
import { getMessages } from "next-intl/server";
import { Container } from "@/ui-components/layout/container";
import Image from "next/image";
import { Button, Text } from "@radix-ui/themes";

function NotFoundContent() {
  const t = useTranslations("NotFound");

  return (
    <Container className="pt-20 pb-30 bg-[#f0f0f0]">
      <div className="section-content flex flex-col items-center justify-center gap-6 text-center">
        <Image
          src="/graphics/orange/404.png"
          alt="404 Not Found"
          width={120}
          height={120}
        />
        <Text color="gray" size="2">
          {t("description")}
        </Text>
        <Button
          size="3"
          variant="soft"
          radius="full"
          color="gray"
          highContrast
          asChild
        >
          <Link href="/">{t("returnHome")}</Link>
        </Button>
      </div>
    </Container>
  );
}

export default async function NotFound() {
  const messages = await getMessages({ locale: "en" });

  return (
    <NextIntlClientProvider locale={"en"} messages={messages}>
      <LandingPageLayout>
        <NotFoundContent />
      </LandingPageLayout>
    </NextIntlClientProvider>
  );
}
