import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON>, <PERSON>o_Mono } from "next/font/google";
import { Theme } from "@radix-ui/themes";
import { getLocale } from "next-intl/server";
import { QueryProvider } from "./query-provider";
import "@radix-ui/themes/styles.css";
import "@repo/ui/radix-override.scss";
import "@repo/ui/globals.scss";

const robotoSans = Roboto({
  variable: "--font-roboto-sans",
  subsets: ["latin"],
});

const robotoMono = Roboto_Mono({
  variable: "--font-roboto-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Oecusse Digital Centre",
  description: "The Future of Blockchain-Powered Investment",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();

  return (
    <html lang={locale}>
      <body
        className={`${robotoSans.variable} ${robotoMono.variable} antialiased`}
      >
        <QueryProvider>
          <Theme accentColor="orange" scaling="100%" grayColor="gray">
            {children}
          </Theme>
        </QueryProvider>
      </body>
    </html>
  );
}
