import { SectionBanner } from "../_components/section-banner";
import { useTranslations } from "next-intl";
import { PrimaryButton } from "@/ui-components/buttons";
import { SectionSingleIntro } from "../_components/section-single-intro";
import { SectionEndCta } from "../_components/section-end-cta";
import { Link } from "@/i18n/navigation";
import { SectionHorizontalSlides } from "../_components/section-horizontal-slides";

export default function Page() {
  const t = useTranslations();

  const slides = Array.from({ length: 5 }).map((_, index) => ({
    image: t(`AboutPage.UniqueFeatures.slides.${index}.image`),
    title: t(`AboutPage.UniqueFeatures.slides.${index}.title`),
    description: t(`AboutPage.UniqueFeatures.slides.${index}.description`),
  }));

  return (
    <main>
      <SectionBanner
        backgroundImage={t("AboutPage.Banner.backgroundImage")}
        caption={t("AboutPage.Banner.caption")}
        title={t("AboutPage.Banner.title")}
        description={t("AboutPage.Banner.description")}
        buttons={
          <PrimaryButton size="3" asChild>
            <Link href="/dashboard">{t("Common.investNow")}</Link>
          </PrimaryButton>
        }
      />
      <SectionHorizontalSlides
        title={t("AboutPage.UniqueFeatures.title")}
        backgroundImage={t("AboutPage.UniqueFeatures.backgroundImage")}
        slides={slides}
      />
      <SectionSingleIntro
        backgroundImage={t("AboutPage.Vision.backgroundImage")}
        title={t("AboutPage.Vision.title")}
        description={t("AboutPage.Vision.description")}
      />
      <SectionSingleIntro
        theme="dark"
        backgroundImage={t("AboutPage.Strategy.backgroundImage")}
        title={t("AboutPage.Strategy.title")}
        description={t("AboutPage.Strategy.description")}
      />
      <SectionEndCta
        backgroundImage={t("AboutPage.EndCta.backgroundImage")}
        title={t("AboutPage.EndCta.title")}
        buttons={
          <div>
            <PrimaryButton size="3" asChild>
              <Link href="/invest">
                {t("HomePage.EndCta.investmentButton")}
              </Link>
            </PrimaryButton>
          </div>
        }
      />
    </main>
  );
}
