import { Container } from "@/ui-components/layout/container";
import style from "../_components/style.module.scss";
import classNames from "classnames";
import { Fragment } from "react";
import { ArrowDownIcon } from "@radix-ui/react-icons";
import { useTranslations } from "next-intl";

export const SectionInvestmentModel = () => {
  const t = useTranslations("InvestPage.InvestmentModel");

  const items = Array.from({ length: 3 }).map((_, index) => ({
    title: t(`items.${index}.title`),
    subtitle: t(`items.${index}.subtitle`),
    description: t(`items.${index}.description`),
    toTitle: t(`items.${index}.toTitle`),
    toSubtitle: t(`items.${index}.toSubtitle`),
  }));

  return (
    <Container className="py-15 md:py-20">
      <div className="section-content flex flex-col">
        <div className="flex flex-col md:flex-row justify-between gap-6 mb-8 md:mb-10">
          <h2 className={classNames(style.heading2, style.textOrange)}>
            {t("title")}
          </h2>
          <p
            className={classNames(
              style.paragraph,
              style.textGray,
              "max-w-[392px]",
            )}
          >
            {t("subtitle")}
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-4 p-4 md:p-6 pb-8 md:pb-8 bg-[#F2F2F2] rounded-t-md">
          {items.map(({ title, subtitle, description }, index) => {
            return (
              <Fragment key={index}>
                <div className="flex-1">
                  <h3 className={classNames(style.heading2, "mb-2")}>
                    {title}
                  </h3>
                  <p className={classNames(style.heading3, "mb-4")}>
                    {subtitle}
                  </p>
                  <p className={classNames(style.paragraph2, style.textGray)}>
                    {description}
                  </p>
                </div>
                {index !== items.length - 1 && (
                  <div
                    className={classNames(
                      style.heading3,
                      "grid md:place-content-center",
                    )}
                  >
                    +
                  </div>
                )}
              </Fragment>
            );
          })}
        </div>

        <div className="h-2 relative z-1 flex justify-center items-center">
          <span className="bg-white rounded-full w-10 h-10 flex justify-center items-center">
            <ArrowDownIcon className={style.textGray} width={18} height={18} />
          </span>
        </div>

        <div className="flex flex-col md:flex-row gap-4 p-4 md:p-6 pt-8 md:pt-8 bg-[#F76B15] text-white rounded-b-md">
          {items.map(({ toTitle, toSubtitle }, index) => {
            return (
              <Fragment key={index}>
                <div className="flex-1">
                  <h3 className={classNames(style.heading2, "mb-2")}>
                    {toTitle}
                  </h3>
                  <p className={classNames(style.heading3)}>{toSubtitle}</p>
                </div>
                {index !== items.length - 1 && (
                  <div
                    className={classNames(
                      style.heading3,
                      "grid md:place-content-center",
                    )}
                  >
                    +
                  </div>
                )}
              </Fragment>
            );
          })}
        </div>
      </div>
    </Container>
  );
};
