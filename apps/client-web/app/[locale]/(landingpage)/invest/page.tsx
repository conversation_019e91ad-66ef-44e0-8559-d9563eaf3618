import { SectionBanner } from "../_components/section-banner";
import { PrimaryButton, SecondaryButton } from "@/ui-components/buttons";
import { Link } from "@/i18n/navigation";
import { useTranslations } from "next-intl";
import { SectionSingleIntro } from "../_components/section-single-intro";
import { SectionEndCta } from "../_components/section-end-cta";
import { SectionHorizontalSlides } from "../_components/section-horizontal-slides";
import { SectionInvestmentModel } from "./section-investment-model";
import { SectionInvestmentManaged } from "./section-investment-managed";

export default function Page() {
  const t = useTranslations();

  const slides = Array.from({ length: 5 }).map((_, index) => ({
    image: t(`InvestPage.Features.slides.${index}.image`),
    title: t(`InvestPage.Features.slides.${index}.title`),
    description: t(`InvestPage.Features.slides.${index}.description`),
  }));

  return (
    <main>
      <SectionBanner
        backgroundImage={t("InvestPage.Banner.backgroundImage")}
        caption={t("InvestPage.Banner.caption")}
        title={t("InvestPage.Banner.title")}
        description={t("InvestPage.Banner.description")}
        buttons={
          <PrimaryButton size="3" asChild>
            <Link href="/dashboard">{t("Common.investNow")}</Link>
          </PrimaryButton>
        }
      />
      <SectionSingleIntro
        backgroundImage={t("InvestPage.Introduction.backgroundImage")}
        title={t("InvestPage.Introduction.title")}
        description={t("InvestPage.Introduction.description")}
        button={
          <PrimaryButton size="3" asChild>
            <Link href="/about">{t("Common.learnMore")}</Link>
          </PrimaryButton>
        }
      />
      <SectionHorizontalSlides
        backgroundImage={t("InvestPage.Features.backgroundImage")}
        title={t("InvestPage.Features.title")}
        description={t("InvestPage.Features.description")}
        slides={slides}
      />
      <SectionInvestmentModel />
      <SectionInvestmentManaged />
      <SectionEndCta
        backgroundImage={t("InvestPage.EndCta.backgroundImage")}
        title={t("InvestPage.EndCta.title")}
        buttons={
          <div>
            <SecondaryButton size="3" asChild>
              <Link href="/about">{t("InvestPage.EndCta.visionButton")}</Link>
            </SecondaryButton>
          </div>
        }
      />
    </main>
  );
}
