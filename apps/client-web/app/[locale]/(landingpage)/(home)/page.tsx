import { useTranslations } from "next-intl";
import { SectionOverview } from "../_components/section-overview";
import { SectionBanner } from "../_components/section-banner";
import { SectionPress } from "../_components/section-press";
import { SectionFaq } from "../_components/section-faq";
import { SectionEndCta } from "../_components/section-end-cta";
import { SectionIntroductionParallax } from "../_components/section-introduction-parallax";
import { PrimaryButton, SecondaryButton } from "@/ui-components/buttons";
import { Link } from "@/i18n/navigation";

export default function LandingPage() {
  const t = useTranslations();

  const features = Array.from({ length: 4 }).map((_, index) => ({
    icon: t(`HomePage.Overview.features.${index}.icon`),
    title: t(`HomePage.Overview.features.${index}.title`),
    description: t(`HomePage.Overview.features.${index}.description`),
  }));

  const introductions = Array.from({ length: 4 }).map((_, index) => ({
    title: t(`HomePage.Introduction.${index}.title`),
    description: t(`HomePage.Introduction.${index}.description`),
    image: t(`HomePage.Introduction.${index}.image`),
    href: t(`HomePage.Introduction.${index}.href`),
  }));

  return (
    <main>
      <SectionBanner
        backgroundImage={t("HomePage.Banner.backgroundImage")}
        caption={t("HomePage.Banner.caption")}
        title={t("HomePage.Banner.title")}
        description={t("HomePage.Banner.joinUsDescription")}
        buttons={
          <>
            <PrimaryButton size="3" asChild>
              <Link href="/dashboard">{t("Common.investNow")}</Link>
            </PrimaryButton>
            <SecondaryButton size="3" asChild>
              <Link href="/about">{t("Common.learnMore")}</Link>
            </SecondaryButton>
          </>
        }
      />
      <SectionIntroductionParallax
        introductions={introductions}
        learnMoreText={t("Common.learnMore")}
      />
      <SectionOverview
        title={t("HomePage.Overview.title")}
        features={features}
      />
      <SectionPress />
      <SectionFaq />
      <SectionEndCta
        backgroundImage={t("HomePage.EndCta.backgroundImage")}
        title={t("HomePage.EndCta.title")}
        buttons={
          <div className="grid grid-rows2 md:grid-cols-2 md:m-auto gap-3 empty:hidden">
            <PrimaryButton size="3" asChild>
              <Link href="/invest">
                {t("HomePage.EndCta.investmentButton")}
              </Link>
            </PrimaryButton>
            <SecondaryButton size="3" asChild>
              <Link href="/about">{t("HomePage.EndCta.visionButton")}</Link>
            </SecondaryButton>
          </div>
        }
      />
    </main>
  );
}
