import { Link } from "@/i18n/navigation";
import classNames from "classnames";
import Image from "next/image";
import style from "./style.module.scss";
import type { LinkConfig } from "@/ui-components/nav-link/utils";
import { Separator } from "@radix-ui/themes";

export const LandingPageFooter = ({ config }: { config: LinkConfig[] }) => {
  return (
    <div className={"text-sm py-10 flex flex-col gap-4"}>
      <div className="flex flex-col md:flex-row-reverse md:justify-between">
        <div className="flex flex-col gap-4 md:flex-row">
          {config.map(({ href, label }) => {
            return (
              <Link key={href} href={href} className="font-medium">
                {label}
              </Link>
            );
          })}
        </div>
        <div className="md:hidden my-6">
          <Separator size="4" />
        </div>
        <Image src="/odc-logo-bw.png" alt="DSC logo" width={65} height={32} />
      </div>
      <div
        className={classNames(style.textGray, "flex flex-col gap-2 md:gap-6")}
      >
        <span>Contact <NAME_EMAIL></span>
        <div className="hidden md:block">
          <Separator size="4" />
        </div>
        <div className="flex flex-col gap-2 md:flex-row md:gap-10">
          <p>© ODC 2018–2025. All rights reserved</p>
          <div className={style.smallLinks}>
            <Link href="/privacy">Privacy</Link>
            <Link href="/terms">Terms</Link>
          </div>
        </div>
      </div>
    </div>
  );
};
