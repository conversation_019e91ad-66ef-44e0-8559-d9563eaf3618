import { Container } from "@/ui-components/layout/container";
import style from "./style.module.scss";
import classNames from "classnames";
import { SecondaryButton } from "@/ui-components/buttons";
import { Link } from "@/i18n/navigation";
import { ParallaxScroll } from "@/ui-components/parallax-scroll";
import { Fragment } from "react";

interface Introduction {
  title: string;
  description: string;
  image: string;
  href: string;
}

interface SectionIntroductionProps {
  introductions: Introduction[];
  learnMoreText: string;
  className?: string;
}

export const SectionIntroductionParallax = ({
  introductions,
  learnMoreText,
  className,
}: SectionIntroductionProps) => {
  return (
    <ParallaxScroll
      items={introductions.map((section, idx) => {
        return (
          <Fragment key={idx}>
            <Container
              className={classNames(
                "h-[100vh] flex flex-col relative w-full -z-10",
                className,
              )}
              background={
                <>
                  <div
                    style={{ backgroundImage: `url(${section.image})` }}
                    className="bg-fixed bg-cover w-full md:w-1/2 absolute top-0 right-0 l-0 h-full -z-10"
                  />
                  <div className="bg-black/50 md:bg-orange-500 w-full md:w-1/2 absolute top-0 left-0 h-full -z-10" />
                </>
              }
            />
            <Container
              dataEffect="parallax"
              className={classNames(
                "h-[100vh] flex flex-col relative w-full -z-10",
                className,
              )}
              background={
                <>
                  <div
                    style={{ backgroundImage: `url(${section.image})` }}
                    className="bg-fixed bg-cover w-full md:w-1/2 absolute top-0 right-0 l-0 h-full -z-10"
                  />
                  <div className="bg-black/50 md:bg-orange-500 w-full md:w-1/2 absolute top-0 left-0 h-full -z-10" />
                </>
              }
            />
            <Container
              dataEffect="parallax"
              className={classNames(
                "h-[100vh] flex flex-col relative w-full -z-10",
                className,
              )}
            >
              <div
                className={classNames(
                  "h-[100vh] col-span-4 md:col-span-6 flex flex-col justify-center gap-10 xl:gap-30 text-white",
                  "py-10 md:py-30 md:pr-3 lg:pr-8 lg:pl-5 xl:pr-11 xl:pl-8",
                )}
              >
                <div className="flex flex-col lg:flex-row gap-10 items-center">
                  <div className="flex-1 flex flex-col gap-6">
                    <h2 className={classNames(style.heading2)}>
                      {section.title}
                    </h2>
                    <p
                      className={classNames(
                        style.paragraph,
                        "whitespace-pre-line",
                      )}
                    >
                      {section.description}
                    </p>

                    <div>
                      <SecondaryButton size="3" asChild>
                        <Link className="mr-auto" href={section.href}>
                          {learnMoreText}
                        </Link>
                      </SecondaryButton>
                    </div>
                  </div>
                </div>
              </div>
            </Container>
          </Fragment>
        );
      })}
    />
  );
};
