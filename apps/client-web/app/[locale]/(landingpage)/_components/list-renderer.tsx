import { Heading, Text } from "@radix-ui/themes";
import classNames from "classnames";
import { Fragment } from "react";

export interface ListItem {
  content: string;
  title?: string;
  children?: ListItem[];
}

const listTypes = ["list-decimal", "list-[lower-alpha]", "list-[lower-roman]"];

export const ListRenderer = ({
  contents,
  level = 0,
}: {
  contents: ListItem[];
  level?: number;
}) => {
  const type = listTypes[level % listTypes.length];
  return (
    <ol className={classNames("pl-6", type)}>
      {contents.map(({ title, content, children }, index) => (
        <Fragment key={index}>
          {title && (
            <Heading size="5" className="relative -left-5 pb-3">
              {title}
            </Heading>
          )}
          <Text size="4" color="gray">
            <li key={index} className={classNames({ "mb-6": level === 0 })}>
              {content}
              {children && (
                <ListRenderer contents={children} level={level + 1} />
              )}
            </li>
          </Text>
        </Fragment>
      ))}
    </ol>
  );
};
