"use client";
import { Container } from "@/ui-components/layout/container";
import classNames from "classnames";
import style from "./style.module.scss";
import { useTranslations } from "next-intl";
import { CollapseItem } from "@/ui-components/collapse-item";

export const SectionFaq = () => {
  const t = useTranslations("HomePage.FAQ");

  return (
    <Container className="py-20 md:py-30">
      <div className="col-span-full md:col-start-2 md:col-span-10 lg:col-start-2 lg:col-span-4 mb-8 md:mb-10">
        <h2
          className={classNames(
            style.heading2,
            style.textOrange,
            "md:text-center lg:text-left",
          )}
        >
          {t("title")}
        </h2>
      </div>
      <div className="col-span-full md:col-start-2 md:col-span-10 lg:col-span-6 divide-y divide-gray-200">
        {Array.from({ length: 4 }).map((_, index) => {
          return (
            <CollapseItem
              key={index}
              title={
                <span className={classNames(style.heading3, "font-medium")}>
                  {t(`items.${index}.question`)}
                </span>
              }
            >
              <div
                className={classNames(style.paragraph2, style.textGray, "pt-4")}
              >
                {t(`items.${index}.answer`)}
              </div>
            </CollapseItem>
          );
        })}
      </div>
    </Container>
  );
};
