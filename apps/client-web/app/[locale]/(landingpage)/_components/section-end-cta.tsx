import { Container } from "@/ui-components/layout/container";
import style from "./style.module.scss";

interface SectionEndCtaProps {
  backgroundImage: string;
  title: string;
  buttons: React.ReactNode;
}

export const SectionEndCta = ({
  backgroundImage,
  title,
  buttons,
}: SectionEndCtaProps) => {
  return (
    <Container
      className="bg-center bg-no-repeat bg-cover py-30 text-white"
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <div className="section-content md:text-center flex flex-col gap-8 md:gap-10">
        <h2 className={style.heading2Lg}>{title}</h2>
        {buttons}
      </div>
    </Container>
  );
};
