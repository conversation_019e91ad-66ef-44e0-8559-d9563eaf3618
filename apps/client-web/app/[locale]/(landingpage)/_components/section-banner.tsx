import { Container } from "@/ui-components/layout/container";
import style from "./style.module.scss";
import classNames from "classnames";

interface SectionBannerProps {
  caption: string;
  title: string;
  description: string;
  backgroundImage: string;
  buttons: React.ReactNode;
}

export const SectionBanner = ({
  caption,
  title,
  description,
  backgroundImage,
  buttons,
}: SectionBannerProps) => {
  return (
    <Container
      className={
        "h-[740px] text-background bg-foreground bg-center bg-no-repeat bg-cover pt-25 pb-20"
      }
      style={{
        backgroundImage: `url("${backgroundImage}")`,
      }}
    >
      <div className="col-span-4 md:col-span-7 lg:col-start-2 lg:col-span-6">
        <p className={classNames(style.caption, "mb-2")}>{caption}</p>
        <h1 className={classNames(style.heading1, "mb-8 md:mb-10")}>{title}</h1>
      </div>
      <div className="col-span-4 md:col-span-4 flex md:justify-end"></div>
      <div className="col-span-4 md:col-start-9 md:col-span-4 lg:col-start-9 lg:col-span-3 flex flex-col md:justify-end">
        <p className={classNames(style.paragraph, "mb-6 md:mb-10")}>
          {description}
        </p>
        <div className="flex flex-col gap-3">{buttons}</div>
      </div>
    </Container>
  );
};
