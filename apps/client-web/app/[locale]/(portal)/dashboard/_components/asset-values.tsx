import { Heading, Text } from "@radix-ui/themes";

interface ValePair {
  name: string;
  value: string | number;
}

export const AssetValues = ({
  main,
  details,
}: {
  main: ValePair;
  details: ValePair[];
}) => {
  return (
    <div className="flex flex-col gap-y-2">
      <Heading
        as="h5"
        size="4"
        weight="medium"
        className="flex justify-between color-primary"
      >
        <span>{main.name}</span>
        <span>{main.value}</span>
      </Heading>
      {details.map((detail) => (
        <div key={detail.name} className="flex justify-between">
          <Heading as="h6" size="3" weight="regular" color="gray">
            {detail.name}
          </Heading>
          <Text size="3" weight="medium">
            {detail.value}
          </Text>
        </div>
      ))}
    </div>
  );
};
