import { PayoutTable } from "./payout-table";
import { Pagination } from "@repo/ui/pagination";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import service from "@/api";

export const PayoutSection = () => {
  const tCommon = useTranslations("Common");
  const [currentPage, setCurrentPage] = useState(1);

  const { data, isLoading } = useQuery({
    queryFn: async () => {
      const res = await service.getInvestmentPayouts({ page: currentPage });
      return res.data?.data;
    },
    queryKey: ["payouts", currentPage],
  });

  const { records, totalPages } = data ?? {};

  return (
    <>
      <PayoutTable data={records ?? []} isLoading={isLoading} />
      {Number(totalPages) > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages ?? 1}
          onPageChange={setCurrentPage}
          className="justify-end"
          nextText={tCommon("next")}
          previousText={tCommon("previous")}
        />
      )}
    </>
  );
};
