import { UserInfoDTO } from "@/api/data-contracts";
import { CopyButton } from "@/ui-components/copy-button";
import { Badge, Card, Heading, Separator, Text } from "@radix-ui/themes";
import { useTranslations } from "next-intl";

export const UserStatus = ({
  userStatus,
}: {
  userStatus: UserInfoDTO["status"];
}) => {
  const t = useTranslations("Portal.Profile.status");

  switch (userStatus) {
    case "PENDING_KYC":
      return (
        <Badge radius="full" color="blue">
          {t("pending")}
        </Badge>
      );
    case "ACTIVE":
      return (
        <Badge radius="full" color="green">
          {t("approved")}
        </Badge>
      );
    case "SUSPENDED":
      return (
        <Badge radius="full" color="red">
          {t("rejected")}
        </Badge>
      );
    case "PERMANENTLY_RESTRICTED":
      return (
        <Badge radius="full" color="red">
          {t("restricted")}
        </Badge>
      );
    default:
      return null;
  }
};

export const ProfileSection = ({ profile = {} }: { profile?: UserInfoDTO }) => {
  const t = useTranslations("Portal.Profile");
  const { publicId, status } = profile;

  const name = profile.name ?? "";

  return (
    <Card size={{ initial: "2", md: "3" }}>
      <div className="flex flex-col md:flex-row md:items-center gap-y-4">
        <div className="flex-1 flex gap-4 items-center">
          <Heading as="h3" size="4" weight="bold">
            {name}
          </Heading>
        </div>
        <div className="flex-1 flex flex-col gap-2">
          <Text size="2" color="gray" weight="medium">
            {t("profileId")}
          </Text>
          <div className="flex gap-2 items-center">
            <Text size="3">{publicId}</Text>
            {publicId && <CopyButton text={publicId} />}
          </div>
        </div>
        <div className="hidden md:block">
          <Separator orientation="vertical" size="2" className="mx-6" />
        </div>
        <div className="flex-1 flex flex-col gap-2">
          <Text size="2" color="gray" weight="medium">
            {t("identityVerification")}
          </Text>
          <div>
            <UserStatus userStatus={status} />
          </div>
        </div>
      </div>
    </Card>
  );
};
