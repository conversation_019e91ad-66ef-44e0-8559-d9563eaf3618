import { TransactionDetailsResponse } from "@/api/data-contracts";
import { AlertDialog, Button, Flex } from "@radix-ui/themes";
import { useTranslations } from "next-intl";
import { TransactionDetailsContent } from "./transaction-detail-content";

interface TransactionDetailsDialogProps {
  data?: TransactionDetailsResponse;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const TransactionDetailsDialog = ({
  data,
  open,
  onOpenChange,
}: TransactionDetailsDialogProps) => {
  const tDetails = useTranslations("Portal.TransactionHistory.detailsDialog");
  const tCommon = useTranslations("Common");

  if (!data) return null;

  return (
    <AlertDialog.Root open={open} onOpenChange={onOpenChange}>
      <AlertDialog.Content maxWidth="450px">
        <AlertDialog.Title size="6" mt="2" mb="5">
          {tDetails("depositDetails")}
        </AlertDialog.Title>

        <TransactionDetailsContent data={data} />

        <Flex gap="3" mt="4" mb="2" justify="end">
          <AlertDialog.Cancel>
            <Button variant="soft" radius="full" size="3" color="gray">
              {tCommon("close")}
            </Button>
          </AlertDialog.Cancel>
        </Flex>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
};
