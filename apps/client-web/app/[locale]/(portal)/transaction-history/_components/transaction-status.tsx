import { Badge } from "@radix-ui/themes";
import { useTranslations } from "next-intl";

export const TransactionStatus = ({ status }: { status?: string }) => {
  const statusT = useTranslations("Portal.TransactionHistory.statusLabels");

  switch (status) {
    case "PENDING":
      return (
        <Badge radius="full" color="blue">
          {statusT("processing")}
        </Badge>
      );
    case "SUCCESSFUL":
      return (
        <Badge radius="full" color="green">
          {statusT("verified")}
        </Badge>
      );
    case "FAILED":
      return (
        <Badge radius="full" color="red">
          {statusT("rejected")}
        </Badge>
      );
    default:
      return "-";
  }
};

export const TransactionType = ({ type }: { type?: string }) => {
  const tType = useTranslations("Portal.TransactionHistory.typeLabels");
  switch (type) {
    case "INVEST_FIAT_DEPOSIT":
      return tType("fiatDeposit");
    case "INVEST_CRYPTO_DEPOSIT":
      return tType("cryptoDeposit");
    default:
      return type || "-";
  }
};
