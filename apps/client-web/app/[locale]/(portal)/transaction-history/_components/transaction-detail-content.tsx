import {
  CryptoDepositDetails,
  FiatDepositDetails,
  TransactionDetailsResponse,
} from "@/api/data-contracts";
import { Text } from "@radix-ui/themes";
import { useTranslations } from "next-intl";
import { CopyButton } from "@/ui-components/copy-button";
import { TransactionStatus, TransactionType } from "./transaction-status";
import { formatDate } from "../utils";
import { AmountDisplay } from "@repo/ui/amount-display";
interface DetailRowProps {
  label: string;
  value: string | number | React.ReactNode;
  enableCopy?: boolean;
  copyText?: string;
}

function isTransactionCrypto(
  detail: CryptoDepositDetails | FiatDepositDetails | undefined,
): detail is CryptoDepositDetails {
  return detail?.depositType === "CRYPTO";
}

const DetailRow = ({ label, value, enableCopy, copyText }: DetailRowProps) => {
  return (
    <div className="flex justify-between">
      <Text size="3" color="gray">
        {label}
      </Text>
      <div className="flex gap-2 max-w-[50%]">
        <Text size="3" className="font-medium text-right break-all">
          {value}
        </Text>
        {enableCopy && copyText && <CopyButton text={copyText} />}
      </div>
    </div>
  );
};

export const TransactionDetailsContent = ({
  data,
}: {
  data: TransactionDetailsResponse;
}) => {
  const t = useTranslations("Portal.TransactionHistory");
  const tDetails = useTranslations("Portal.TransactionHistory.detailsDialog");

  const isRejected = data.status === "FAILED";

  return (
    <div className="flex flex-col gap-3">
      <DetailRow
        label={t("transactionId")}
        value={data.id || "-"}
        enableCopy={!!data.id}
        copyText={data.id}
      />
      <DetailRow
        label={t("type")}
        value={<TransactionType type={data.type} />}
      />
      <DetailRow
        label={tDetails("amount")}
        value={
          <AmountDisplay
            amount={data.amount?.amount}
            showSign={!isRejected}
            dimmed={isRejected}
          />
        }
      />
      <DetailRow label={t("currency")} value={data.amount?.currency || "-"} />
      {isTransactionCrypto(data.details) && data.details.transactionHash && (
        <DetailRow
          label={t("transactionHash")}
          value={data.details.transactionHash}
          enableCopy={true}
          copyText={data.details.transactionHash}
        />
      )}
      <DetailRow
        label={tDetails("createdDate")}
        value={formatDate(data.createdAt)}
      />
      <DetailRow
        label={t("status")}
        value={<TransactionStatus status={data.status} />}
      />
      <DetailRow
        label={t("blockedReason")}
        value={data.details?.rejectionReason || "-"}
      />
    </div>
  );
};
