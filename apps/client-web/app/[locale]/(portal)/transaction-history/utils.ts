export const formatDate = (dateString: string | undefined) => {
  // DD/MM/YY HH:MM:SS
  if (!dateString) return "-";
  try {
    const date = new Date(dateString);
    return date.toLocaleString("en-SG", {
      day: "2-digit",
      month: "2-digit",
      year: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  } catch {
    return dateString;
  }
};
