"use client";
import { Container } from "@/ui-components/layout/container";
import { goToLogout } from "@/utils/logout";
import { Button } from "@radix-ui/themes";
import { InfoLayout } from "@repo/ui/info-layout";
import { useTranslations } from "next-intl";

const Page = () => {
  const t = useTranslations("Common");

  return (
    <Container>
      <div className="col-span-full md:col-start-3 md:col-span-8 lg:col-start-4 lg:col-span-6 py-6 md-py-10">
        <InfoLayout
          className="py-20 md:py-10 xl:py-20"
          icon="/graphics/orange/compliance-x.png"
          iconAlt="verifying deposit"
          title={t("accessRestricted")}
          description={t("accessRestrictedDescription")}
        >
          <div className="flex flex-col md:mx-auto">
            <Button
              size="3"
              variant="soft"
              color="gray"
              radius="full"
              highContrast
              onClick={goToLogout}
            >
              {t("signOut")}
            </Button>
          </div>
        </InfoLayout>
      </div>
    </Container>
  );
};

export default Page;
