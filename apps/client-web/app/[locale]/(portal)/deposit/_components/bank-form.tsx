import { PrimaryButton } from "@repo/ui/form-button";
import { InputField } from "@/ui-components/form/text-field";
import { Card } from "@radix-ui/themes";
import { FormSection } from "../../_components/form-layout";
import { InfoCallout } from "./info-callout";
import { StepTitle } from "./step-title";
import { FileField } from "@/ui-components/form/file-field";
import { useMutation } from "@tanstack/react-query";
import service from "@/api";
import { useCallback, useState } from "react";
import { everyPropHsValue } from "@/utils/everyPropHsValue";
import { getJsonBlob } from "@/utils/json-blob";
import { useTranslations } from "next-intl";

interface BankDepositParams {
  bankName: string;
  accountNumber: string;
  amount: number;
  uploadDocument: File;
}

export const BankForm = ({ goNext }: { goNext: () => void }) => {
  const t = useTranslations("Portal.Deposit.bankForm");
  const { mutate } = useMutation({
    mutationFn: service.submitFiatDeposit,
    onSuccess: goNext,
  });

  const [data, setData] = useState<Partial<BankDepositParams>>({
    bankName: undefined,
    accountNumber: undefined,
    amount: undefined,
    uploadDocument: undefined,
  });

  const onValueChange = useCallback(
    <K extends keyof BankDepositParams>(
      value: BankDepositParams[K] | undefined,
      key?: K,
    ) => {
      if (!key) return;
      setData((prev) => ({
        ...prev,
        [key]: value,
      }));
    },
    [],
  );

  const onSubmit = () => {
    if (!everyPropHsValue(data)) return;
    const { uploadDocument, ...rest } = data;
    mutate({
      data: getJsonBlob(rest),
      proofOfTransfer: uploadDocument,
    });
  };

  return (
    <FormSection>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle step={1} title={t("step1Title")} className="mb-6" />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <InputField<BankDepositParams>
            name="bankName"
            title={t("bankName")}
            placeholder={t("bankNamePlaceholder")}
            required
            onChange={onValueChange}
          />
          <InputField<BankDepositParams>
            name="accountNumber"
            title={t("accountNumber")}
            placeholder={t("accountNumberPlaceholder")}
            required
            onChange={onValueChange}
          />

          <InputField<BankDepositParams>
            name="amount"
            title={t("depositAmount")}
            placeholder={t("depositAmountPlaceholder")}
            remarks={t("depositAmountRemarks")}
            required
            onChange={onValueChange}
          />

          <InfoCallout>{t("minimumDepositInfo")}</InfoCallout>

          <InfoCallout>{t("multiplesInfo")}</InfoCallout>
        </div>
      </Card>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle step={2} title={t("step2Title")} className="mb-6" />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <InputField
            title={t("recipientBankName")}
            value="PT. BANK MUAMALAT INDONESIA (SWIFT: MUABIDJA)"
            disabled
            enableCopy
          />
          <InputField
            title={t("recipientAccountNumber")}
            value="**********"
            disabled
            enableCopy
          />
          <InputField
            className="col-span-full"
            title={t("recipientName")}
            value="EUROPEAN CREDIT INVESTMENT BANK LTD"
            disabled
            enableCopy
          />
          <InfoCallout>{t("bankAccountNameMatch")}</InfoCallout>
          <InfoCallout>{t("amountMatch")}</InfoCallout>
        </div>
      </Card>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle step={3} title={t("step3Title")} className="mb-6" />
        <FileField
          title={t("uploadDocument")}
          accept="image/jpg,image/jpeg,image/png"
          required
          name="uploadDocument"
          value={data.uploadDocument}
          onChange={(file) => {
            onValueChange(file, "uploadDocument");
          }}
        />
      </Card>
      <PrimaryButton disabled={!everyPropHsValue(data)} onClick={onSubmit}>
        {t("confirmDeposit")}
      </PrimaryButton>
    </FormSection>
  );
};
