import { Card } from "@radix-ui/themes";
import { FormSection } from "../../_components/form-layout";
import { PrimaryButton } from "@repo/ui/form-button";
import { StepTitle } from "./step-title";
import { InputField } from "@/ui-components/form/text-field";
import { InfoCallout } from "./info-callout";
import service from "@/api";
import { useCallback, useState } from "react";
import { SubmitCryptoDepositRequest } from "@/api/data-contracts";
import { useMutation } from "@tanstack/react-query";
import { everyPropHsValue } from "@/utils/everyPropHsValue";
import { useTranslations } from "next-intl";

export const CryptoForm = ({ goNext }: { goNext: () => void }) => {
  const t = useTranslations("Portal.Deposit.cryptoForm");
  const [data, setData] = useState<Partial<SubmitCryptoDepositRequest>>({
    userWalletAddress: undefined,
    amount: undefined,
    transactionHash: undefined,
  });

  const { mutate } = useMutation({
    mutationFn: service.submitCryptoDeposit,
    onSuccess: goNext,
  });

  const onSubmit = async () => {
    if (!everyPropHsValue(data)) return;
    mutate(data);
  };

  const onValueChange = useCallback(
    <K extends keyof SubmitCryptoDepositRequest>(
      value: SubmitCryptoDepositRequest[K] | undefined,
      key?: K,
    ) => {
      if (!key) return;
      setData((prev) => ({
        ...prev,
        [key]: value,
      }));
    },
    [],
  );

  return (
    <FormSection>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle step={1} title={t("step1Title")} className="mb-6" />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <InputField<SubmitCryptoDepositRequest>
            name="userWalletAddress"
            title={t("walletAddress")}
            placeholder={t("walletAddressPlaceholder")}
            required
            onChange={onValueChange}
          />

          <InputField<SubmitCryptoDepositRequest>
            name="amount"
            title={t("depositAmount")}
            placeholder={t("depositAmountPlaceholder")}
            remarks={t("depositAmountRemarks")}
            required
            onChange={onValueChange}
          />

          <InfoCallout>{t("minimumDepositInfo")}</InfoCallout>

          <InfoCallout>{t("multiplesInfo")}</InfoCallout>
        </div>
      </Card>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle step={2} title={t("step2Title")} className="mb-6" />
        <div className="grid grid-cols-1 gap-6">
          <InputField
            title={t("walletAddress")}
            value="******************************************"
            disabled
            enableCopy
          />
          <InfoCallout>{t("onlySendUSDT")}</InfoCallout>
          <InfoCallout>{t("tronNetworkWarning")}</InfoCallout>
          <InfoCallout>{t("amountMatch")}</InfoCallout>
        </div>
      </Card>
      <Card size={{ initial: "2", md: "3" }}>
        <StepTitle step={3} title={t("step3Title")} className="mb-6" />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <InputField<SubmitCryptoDepositRequest>
            name="transactionHash"
            title={t("transactionHash")}
            placeholder={t("transactionHashPlaceholder")}
            required
            onChange={onValueChange}
          />
        </div>
      </Card>
      <PrimaryButton onClick={onSubmit} disabled={!everyPropHsValue(data)}>
        {t("confirmDeposit")}
      </PrimaryButton>
    </FormSection>
  );
};
