import { Card, Heading, RadioGroup, Separator, Text } from "@radix-ui/themes";
import { FormSection } from "../../_components/form-layout";
import { DepositMethod } from "./constants";
import { SelectField } from "@/ui-components/form/select-field";
import { PrimaryButton } from "@repo/ui/form-button";
import { useTranslations } from "next-intl";

export const SelectMethod = ({
  depositMethod,
  setDepositMethod,
  goNext,
}: {
  depositMethod: string;
  setDepositMethod: (depositMethod: string) => void;
  goNext: () => void;
}) => {
  const t = useTranslations("Portal.Deposit");

  return (
    <FormSection>
      <Card size={{ initial: "2", md: "3" }} className="min-h-60">
        <div className="flex flex-col gap-4">
          <Heading as="h3" size="5">
            {t("depositFunds")}
          </Heading>
          <Separator size="4" />

          <div className="flex flex-col gap-2">
            <Heading as="h5" size="2" weight="medium">
              {t("depositMethod")}
            </Heading>
            <RadioGroup.Root
              color="gray"
              highContrast
              value={depositMethod}
              onValueChange={setDepositMethod}
            >
              <div className="flex flex-col gap-2">
                <RadioGroup.Item value={DepositMethod.BankTransfer}>
                  {t("bankTransfer")}
                </RadioGroup.Item>
                <RadioGroup.Item value={DepositMethod.CryptoCurrency}>
                  {t("cryptoCurrency")}
                </RadioGroup.Item>
              </div>
            </RadioGroup.Root>
          </div>
          {depositMethod === DepositMethod.BankTransfer && (
            <Text size="2" color="gray">
              {t("bankTransferDescription")}
            </Text>
          )}
          {depositMethod === DepositMethod.CryptoCurrency && (
            <>
              <SelectField<{ network: string }>
                title={t("network")}
                name="network"
                value="TRON"
                options={[{ value: "TRON", label: "TRON" }]}
                disabled
              />

              <SelectField<{ cryptoCurrency: string }>
                title={t("selectCrypto")}
                name="cryptoCurrency"
                value="USDT"
                options={[{ value: "USDT", label: "USDT" }]}
                disabled
              />
            </>
          )}
        </div>
      </Card>
      <PrimaryButton onClick={goNext}>{t("continue")}</PrimaryButton>
    </FormSection>
  );
};
