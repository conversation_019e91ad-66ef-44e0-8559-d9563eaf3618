import { InfoCircledIcon } from "@radix-ui/react-icons";
import { Callout } from "@radix-ui/themes";
import { PropsWithChildren } from "react";

export const InfoCallout = ({ children }: PropsWithChildren) => {
  return (
    <Callout.Root size="1" color="cyan" highContrast className="col-span-full">
      <Callout.Icon>
        <InfoCircledIcon />
      </Callout.Icon>
      <Callout.Text>{children}</Callout.Text>
    </Callout.Root>
  );
};
