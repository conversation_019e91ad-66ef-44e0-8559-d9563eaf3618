import { Badge, Heading } from "@radix-ui/themes";
import classNames from "classnames";

export const StepTitle = ({
  step,
  title,
  className,
}: {
  step: number | string;
  title: string;
  className?: string;
}) => {
  return (
    <div
      className={classNames(
        "flex flex-col md:flex-row gap-2 md:gap-4 items-start md:items-center",
        className,
      )}
    >
      <Badge color="gray" radius="full" size="3">
        Step {step}
      </Badge>
      <Heading as="h3" size="3">
        {title}
      </Heading>
    </div>
  );
};
