import { Heading, Text } from "@radix-ui/themes";
import { PropsWithChildren, ReactNode } from "react";

export const StepTitle = ({
  title,
  description,
}: {
  title: string;
  description?: ReactNode;
}) => {
  return (
    <div className="col-span-full md:col-start-2 md:col-span-3 flex flex-col gap-4 mb-4">
      <Heading as="h2" size="7" weight="medium">
        {title}
      </Heading>
      {description && (
        <Text size="2" color="gray">
          {description}
        </Text>
      )}
    </div>
  );
};

export const FormSection = ({ children }: PropsWithChildren) => {
  return (
    <div className="col-span-full md:col-span-7 flex flex-col gap-6">
      {children}
    </div>
  );
};
