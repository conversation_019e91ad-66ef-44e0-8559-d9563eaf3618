import { PropsWithChildren } from "react";
import { ToastProvider } from "@repo/ui/toast-provider/index";
import { Container } from "@/ui-components/layout/container";
import { AppNav } from "@/ui-components/nav-link";
import { Toast } from "@/utils/toast";
import "./layout.scss";

const DashboardLayout = ({ children }: PropsWithChildren) => {
  return (
    <ToastProvider instance={Toast}>
      <div className="min-h-dvh flex flex-col">
        <nav className="sticky top-0 bg-white z-10 border-b border-gray-200">
          <Container>
            <div className="section-content">
              <AppNav />
            </div>
          </Container>
        </nav>
        {children}
      </div>
    </ToastProvider>
  );
};

export default DashboardLayout;
