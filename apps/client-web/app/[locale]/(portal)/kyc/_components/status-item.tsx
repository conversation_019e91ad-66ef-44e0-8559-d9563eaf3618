"use client";
import { Badge, Heading } from "@radix-ui/themes";
import classNames from "classnames";
import Image from "next/image";
import { useTranslations } from "next-intl";

export interface StatusItemProps {
  icon: string;
  title: string;
  status: "complete" | "pending" | null;
}

export const StatusItem = ({ icon, title, status }: StatusItemProps) => {
  const t = useTranslations("Portal.Profile.status");

  const renderStatus = () => {
    switch (status) {
      case "complete":
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect width="20" height="20" rx="10" fill="#EF630D" />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M15.2907 4.9692C15.6759 5.22106 15.784 5.73752 15.5321 6.12272L9.86545 14.7894C9.7319 14.9937 9.51554 15.129 9.27349 15.1599C9.03142 15.1909 8.78798 15.1141 8.60741 14.9499L4.94074 11.6166C4.6002 11.307 4.5751 10.78 4.88469 10.4395C5.19428 10.0989 5.72132 10.0738 6.06186 10.3834L9.00536 13.0593L14.1372 5.21064C14.3891 4.82544 14.9055 4.71734 15.2907 4.9692Z"
              fill="white"
            />
          </svg>
        );

      case "pending":
        return (
          <Badge radius="full" color="orange">
            {t("pending")}
          </Badge>
        );
      default:
        return <div className="w-5 h-5"></div>;
    }
  };

  return (
    <>
      <div className="flex gap-6 items-center min-h-6">
        <Image
          src={icon}
          alt="verify"
          width={56}
          height={56}
          className={classNames("p-1 hidden md:block", {
            ["saturate-0"]: !status,
          })}
        />
        <Heading
          as="h5"
          size={{ initial: "3", md: "5" }}
          weight="medium"
          className="grow-1"
          color={!status ? "gray" : undefined}
        >
          {title}
        </Heading>
        {renderStatus()}
      </div>
      <div className="last:hidden py-0.5">
        <div
          className={classNames(
            "w-7 h-3 md:h-5 border-r-1 md:border-r-2 border-gray-400",
            {
              "border-orange-500": status === "complete",
              "border-dashed": status === "pending",
            },
          )}
        ></div>
      </div>
    </>
  );
};
