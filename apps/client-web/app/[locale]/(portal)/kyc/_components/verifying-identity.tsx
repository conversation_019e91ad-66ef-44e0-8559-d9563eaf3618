import { goToLogout } from "@/utils/logout";
import { SecondaryButton } from "@repo/ui/form-button";
import { InfoLayout } from "@repo/ui/info-layout";
import { useTranslations } from "next-intl";

export const VerifyingIdentity = () => {
  const t = useTranslations("Portal.KYC.verifyingIdentity");
  const tCommon = useTranslations("Portal.KYC");

  return (
    <InfoLayout
      className="py-20 md:py-10 xl:py-20"
      icon="/graphics/orange/pending.png"
      iconAlt="pending"
      title={t("verifyingTitle")}
      description={t("verifyingDescription")}
    >
      <div className="flex justify-center">
        <SecondaryButton onClick={goToLogout}>
          {tCommon("signOut")}
        </SecondaryButton>
      </div>
    </InfoLayout>
  );
};
