"use client";
import { Container } from "@/ui-components/layout/container";
import { InputField } from "@/ui-components/form/text-field";
import { ArrowLeftIcon } from "@radix-ui/react-icons";
import { <PERSON><PERSON>, Card, Text } from "@radix-ui/themes";
import { useCallback, useMemo, useState } from "react";
import { SelectField } from "@/ui-components/form/select-field";
import type {
  KycOnboardingMultipartFormData,
  KycVerificationRequestData,
} from "@/api/data-contracts";
import { PrimaryButton, SecondaryButton } from "@repo/ui/form-button";
import { AccountStatus } from "./account-status";
import { Link } from "@/i18n/navigation";
import { FileField } from "@/ui-components/form/file-field";
import { VerifyingIdentity } from "./verifying-identity";
import { FormSection, StepTitle } from "../../_components/form-layout";
import { useMutation, useQuery } from "@tanstack/react-query";
import service from "@/api";
import { getJsonBlob } from "@/utils/json-blob";
import { everyPropHsValue } from "@/utils/everyPropHsValue";
import { useTranslations } from "next-intl";
import { goToLogout } from "@/utils/logout";

type FileData = Pick<
  KycOnboardingMultipartFormData,
  "documentBack" | "documentFront" | "selfie"
>;

type IdentityDocumentOption = {
  value: KycVerificationRequestData["identityDocumentType"];
  label: string;
};

export const KycSteps = () => {
  const t = useTranslations("Portal.KYC");
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [kycData, setKycData] = useState<Partial<KycVerificationRequestData>>(
    {},
  );
  const [fileData, setFileData] = useState<FileData>({});

  const { data: identityDocumentOptions = [] } = useQuery({
    queryFn: async () => {
      const res = await service.getIdentityDocumentTypes();
      const availableList = res.data?.data ?? [];
      return (
        [
          { value: "NATIONAL_ID", label: t("nationalId") },
          { value: "DRIVERS_LICENSE", label: t("driversLicense") },
          { value: "PASSPORT", label: t("passport") },
        ] satisfies IdentityDocumentOption[]
      ).filter((item) => {
        return availableList?.some((type) => type.name === item.value);
      });
    },
    queryKey: ["identity-document-types"],
  });

  const { data: countryList } = useQuery({
    queryFn: service.getCountries,
    queryKey: ["country-list"],
  });

  const goPrevious = () => setCurrentStep((prev) => Math.max(0, prev - 1));
  const goNext = () => setCurrentStep((prev) => Math.min(5, prev + 1));

  const { mutate } = useMutation({
    mutationFn: service.submit,
    onSuccess: goNext,
  });

  const onSubmit = () => {
    if (!everyPropHsValue(kycData)) return;
    mutate({ data: getJsonBlob(kycData), ...fileData });
  };

  const onValueChange = useCallback(
    <K extends keyof KycVerificationRequestData>(
      value: KycVerificationRequestData[K] | undefined,
      key?: K,
    ) => {
      if (!key) return;
      setKycData((prev) => ({
        ...prev,
        [key]: value,
      }));
    },
    [],
  );

  const countries = useMemo(() => {
    return (
      countryList?.data?.data?.map(({ alpha2 = "", name = "" }) => ({
        value: alpha2,
        label: name,
      })) ?? []
    );
  }, [countryList?.data?.data]);

  const logoutButton = (
    <SecondaryButton onClick={goToLogout}>{t("signOut")}</SecondaryButton>
  );

  const renderForm = () => {
    if (currentStep === 1) {
      return (
        <>
          <StepTitle
            title={t("letUsKnowMore")}
            description={t("legallyRequired")}
          />
          <FormSection>
            <Card size={{ initial: "2", md: "3" }} className="min-h-60">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <InputField<KycVerificationRequestData>
                  key="firstName"
                  title={t("firstName")}
                  placeholder={t("firstNamePlaceholder")}
                  name="firstName"
                  required
                  value={kycData.firstName ?? ""}
                  onChange={onValueChange}
                />

                <InputField<KycVerificationRequestData>
                  key="lastName"
                  title={t("lastName")}
                  placeholder={t("lastNamePlaceholder")}
                  name="lastName"
                  required
                  value={kycData.lastName ?? ""}
                  onChange={onValueChange}
                />

                <SelectField<KycVerificationRequestData>
                  key="nationality"
                  title={t("nationality")}
                  name="nationality"
                  placeholder={t("nationalityPlaceholder")}
                  required
                  options={countries}
                  value={kycData.nationality ?? ""}
                  onChange={onValueChange}
                />

                <SelectField<KycVerificationRequestData>
                  key="countryOfResidence"
                  title={t("countryOfResidence")}
                  name="countryOfResidence"
                  placeholder={t("countryOfResidencePlaceholder")}
                  required
                  options={countries}
                  value={kycData.countryOfResidence ?? ""}
                  onChange={onValueChange}
                />
              </div>
            </Card>
            <div className="flex flex-col gap-4">
              <PrimaryButton
                disabled={
                  !(
                    kycData.firstName &&
                    kycData.lastName &&
                    kycData.nationality &&
                    kycData.countryOfResidence
                  )
                }
                onClick={goNext}
              >
                {t("next")}
              </PrimaryButton>
              {logoutButton}
            </div>
          </FormSection>
        </>
      );
    }

    if (currentStep === 2) {
      return (
        <>
          <StepTitle
            title={t("selectIdentityType")}
            description={
              <p>
                {t("biometricConsent")}{" "}
                <Text asChild className="color-primary" weight="bold">
                  <Link href="/privacy">{t("privacyPolicy")}</Link>
                </Text>
                .
              </p>
            }
          />
          <FormSection>
            <Card size={{ initial: "2", md: "3" }} className="min-h-60">
              <SelectField<KycVerificationRequestData>
                title={t("idType")}
                required
                name="identityDocumentType"
                placeholder={t("idTypePlaceholder")}
                options={identityDocumentOptions}
                value={kycData.identityDocumentType?.toString() ?? ""}
                onChange={onValueChange}
              />
            </Card>
            <div className="flex flex-col gap-4">
              <PrimaryButton
                disabled={kycData.identityDocumentType == undefined}
                onClick={goNext}
              >
                {t("next")}
              </PrimaryButton>
              {logoutButton}
            </div>
          </FormSection>
        </>
      );
    }

    if (currentStep === 3) {
      const needBackSide = kycData.identityDocumentType !== "PASSPORT";

      return (
        <>
          <StepTitle
            title={t("uploadImages")}
            description={t("uploadImagesDescription")}
          />
          <FormSection>
            <Card size={{ initial: "2", md: "3" }} className="min-h-60">
              <div className="grid grid-cols-1 gap-4">
                <Text size="3" weight="medium">
                  {t("idClearInstructions")}
                </Text>

                <FileField
                  key="documentFront"
                  title={t("frontSideDocument")}
                  accept="image/jpg,image/jpeg,image/png"
                  required
                  name="documentFront"
                  value={fileData.documentFront}
                  onChange={(file) => {
                    setFileData((prev) => ({
                      ...prev,
                      documentFront: file,
                    }));
                  }}
                />
                {needBackSide && (
                  <FileField
                    key="documentBack"
                    title={t("backSideDocument")}
                    accept="image/jpg,image/jpeg,image/png"
                    required
                    name="documentBack"
                    value={fileData.documentBack}
                    onChange={(file) => {
                      setFileData((prev) => ({
                        ...prev,
                        documentBack: file,
                      }));
                    }}
                  />
                )}
              </div>
            </Card>
            <div className="flex flex-col gap-4">
              <PrimaryButton
                disabled={
                  !fileData.documentFront ||
                  (!fileData.documentBack && needBackSide)
                }
                onClick={goNext}
              >
                {t("upload")}
              </PrimaryButton>
              {logoutButton}
            </div>
          </FormSection>
        </>
      );
    }

    if (currentStep === 4) {
      return (
        <>
          <StepTitle
            title={t("uploadSelfie")}
            description={t("uploadSelfieDescription")}
          />
          <FormSection>
            <Card size={{ initial: "2", md: "3" }} className="min-h-60">
              <div className="grid grid-cols-1 gap-4">
                <Text size="3" weight="medium">
                  {t("selfieInstructions")}
                </Text>

                <FileField
                  key="selfie"
                  title={t("selfieImage")}
                  accept="image/jpg,image/jpeg,image/png"
                  required
                  name="selfie"
                  value={fileData.selfie}
                  onChange={(file) => {
                    setFileData((prev) => ({
                      ...prev,
                      selfie: file,
                    }));
                  }}
                />
              </div>
            </Card>
            <div className="flex flex-col gap-4">
              <PrimaryButton disabled={!fileData.selfie} onClick={onSubmit}>
                {t("submitVerification")}
              </PrimaryButton>
              {logoutButton}
            </div>
          </FormSection>
        </>
      );
    }
  };

  if (currentStep == 0) {
    return (
      <Container>
        <div className="col-span-full md:col-start-3 md:col-span-8 lg:col-start-4 lg:col-span-6 py-6 md-py-10">
          <AccountStatus onStartVerification={goNext} />
        </div>
      </Container>
    );
  }

  if (currentStep < 5) {
    return (
      <Container>
        <div className="col-span-full md:col-start-2 md:col-span-10 pt-6 md:pt-10 mb-6">
          <Button
            variant="soft"
            radius="full"
            color="gray"
            onClick={goPrevious}
          >
            <ArrowLeftIcon /> {t("previousPage")}
          </Button>
        </div>
        {renderForm()}
      </Container>
    );
  }

  return <VerifyingIdentity />;
};
