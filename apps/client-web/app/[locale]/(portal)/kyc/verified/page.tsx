"use client";
import { useAuth } from "@/hooks/use-auth";
import { redirect } from "next/navigation";
import { useKycStatus } from "@/hooks/use-kyc-status";
import { IdentityVerified } from "../_components/identity-verified";
import { Container } from "@/ui-components/layout/container";

const Page = () => {
  useAuth({ requireAuth: true });
  const { kycStatus } = useKycStatus();

  if (kycStatus === undefined) {
    return null;
  }

  if (kycStatus !== null && kycStatus !== "APPROVED") {
    redirect("/kyc");
  }

  return (
    <main className="flex-1 flex flex-col gpa-6 bg-[#F2F2F2]">
      <Container>
        <div className="col-span-full md:col-start-3 md:col-span-8 lg:col-start-4 lg:col-span-6 py-6 md-py-10">
          <IdentityVerified />
        </div>
      </Container>
    </main>
  );
};

export default Page;
