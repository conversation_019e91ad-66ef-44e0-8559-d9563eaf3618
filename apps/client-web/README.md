# Pi<PERSON>lo Frontend

The official frontend application for the Oecusse Digital Centre (ODC), built with Next.js and React. This multilingual platform serves as the digital gateway to ODC's blockchain-powered investment ecosystem.

## Overview

This private repository contains the source code for ODC's web platform, featuring:

- Landing page showcasing ODC's blockchain investment opportunities
- Multilingual support for global investors (English and Chinese)
- Comprehensive information about ODC's digital ecosystem
- Investment model explanations and documentation

## Features

- 🌐 Multilingual support (English and Chinese)
- ⚡ Built with Next.js 15.2 and React 19
- 🎨 Styled with Tailwind CSS and SASS
- 🔍 SEO optimized
- 📱 Fully responsive design
- 🎯 Type-safe with TypeScript
- 🛠 Component library with Radix UI

## Getting Started

### Prerequisites

- Node.js 18.18.0 or later (as specified in Next.js requirements)
- npm, yarn, or pnpm
- Access to ODC's private repository

### Installation

1. Clone the private repository (requires authentication):

```bash
git clone <private-repository-url>
cd piccolo-frontend
```

2. Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Start the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Access the development server at [http://localhost:3000](http://localhost:3000)

## Project Structure

```
piccolo-frontend/
├── app/                    # Next.js app directory
│   ├── [locale]/          # Locale-specific routes
│   │   └── (landingpage)/ # ODC landing page components
│   └── layout.tsx         # Root layout
├── api/                    # API client and types
├── i18n/                  # Internationalization system
│   ├── routing.ts         # Language routing configuration
│   ├── navigation.ts      # Navigation utilities
│   └── request.ts         # i18n request handling
├── messages/              # Translation assets
│   ├── en.json           # English content
│   └── zh.json           # Chinese content
└── public/               # Static assets
    └── graphics/         # Image assets
```

## Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production deployment
- `npm run start` - Start production server
- `npm run lint` - Run ESLint checks
- `npm run api:gen` - Generate TypeScript API client from Swagger

## Tech Stack

- [Next.js](https://nextjs.org/) - React framework (v15.2.1)
- [React](https://reactjs.org/) - UI library (v19.0.0)
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [next-intl](https://next-intl-docs.vercel.app/) - Internationalization (v3.26.5)
- [Radix UI](https://www.radix-ui.com/) - Component library (v3.2.1)
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS
- [SASS](https://sass-lang.com/) - CSS preprocessor (v1.85.1)
- [React Query](https://tanstack.com/query/latest) - Data fetching (v5.67.2)

## API Integration

The application integrates with the ODC backend API. API types and client are auto-generated using swagger-typescript-api:

- **Staging API Documentation**: [https://portal-backend-staging.up.railway.app/v3/api-docs](https://portal-backend-staging.up.railway.app/v3/api-docs)
- **Swagger UI**: [https://portal-backend-staging.up.railway.app/swagger-ui/index.html](https://portal-backend-staging.up.railway.app/swagger-ui/index.html)

To update the API client:

```bash
npm run api:gen
```

## Deployment

The application is configured for deployment on Railway.app:

1. Ensure you have Railway.app access and CLI installed
2. Login to Railway:

```bash
railway login
```

3. Link your project:

```bash
railway link
```

4. Deploy to Railway:

```bash
railway up
```

For manual deployment:

```bash
# Build the application
npm run build

# Start the production server
npm run start
```

Environment variables should be configured in Railway.app dashboard under the project settings.

## Security

This is a private repository containing proprietary code. Please ensure:

- Do not share access credentials
- Do not expose sensitive configuration
- Follow security best practices
- Report any security concerns to the security team

## Contributing

1. Ensure you have the necessary permissions
2. Create a feature branch (`git checkout -b feature/NewFeature`)
3. Commit changes (`git commit -m 'Add NewFeature'`)
4. Push to the branch (`git push origin feature/NewFeature`)
5. Create a Pull Request for review

## Proprietary Notice

This software is proprietary and confidential. Unauthorized copying, modification, distribution, or use of this software, via any medium, is strictly prohibited.

© 2024 Oecusse Digital Centre. All rights reserved.
