<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_918_130897" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="120" height="120">
<rect width="120" height="120" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_918_130897)">
<g clip-path="url(#paint0_angular_918_130897_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.000141509 0.0157075 0.0157075 -0.000141509 30.1415 86.7075)"><foreignObject x="-1072.83" y="-1072.83" width="2145.67" height="2145.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="15" cy="15" r="15" transform="matrix(-1 0 0 1 45 71)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:0.28301796317100525,&#34;m01&#34;:31.415094375610352,&#34;m02&#34;:14.292453765869141,&#34;m10&#34;:31.415094375610352,&#34;m11&#34;:-0.28301796317100525,&#34;m12&#34;:71.141510009765625},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint1_angular_918_130897_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.000141509 0.0157075 0.0157075 -0.000141509 90.1415 86.7075)"><foreignObject x="-1072.83" y="-1072.83" width="2145.67" height="2145.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="15" cy="15" r="15" transform="matrix(-1 0 0 1 105 71)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:0.28301796317100525,&#34;m01&#34;:31.415094375610352,&#34;m02&#34;:74.292449951171875,&#34;m10&#34;:31.415094375610352,&#34;m11&#34;:-0.28301796317100525,&#34;m12&#34;:71.141510009765625},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint2_angular_918_130897_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-0.0148585 0 0 -0.0148585 59.8585 86.7075)"><foreignObject x="-1124.44" y="-1124.44" width="2248.89" height="2248.89"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="60" cy="86" r="15" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-29.716979980468750,&#34;m01&#34;:-2.4054962882713582e-11,&#34;m02&#34;:74.716979980468750,&#34;m10&#34;:-7.8659621134333224e-13,&#34;m11&#34;:-29.716979980468750,&#34;m12&#34;:101.56604003906250},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint3_angular_918_130897_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0148585 1.29897e-09 -1.29896e-09 0.0148585 75.1415 59.2925)"><foreignObject x="-1124.44" y="-1124.44" width="2248.89" height="2248.89"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="75" cy="60" r="15" transform="rotate(-180 75 60)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:29.716979980468750,&#34;m01&#34;:-2.5979168185585877e-06,&#34;m02&#34;:60.283023834228516,&#34;m10&#34;:2.5979416022892110e-06,&#34;m11&#34;:29.716979980468750,&#34;m12&#34;:44.43395996093750},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint4_angular_918_130897_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-0.0148585 -2.59794e-09 2.59793e-09 -0.0148585 44.8585 60.7076)"><foreignObject x="-1124.44" y="-1124.44" width="2248.89" height="2248.89"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="45" cy="60" r="15" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-29.716979980468750,&#34;m01&#34;:5.1958577387267724e-06,&#34;m02&#34;:59.716972351074219,&#34;m10&#34;:-5.1958822950837202e-06,&#34;m11&#34;:-29.716979980468750,&#34;m12&#34;:75.56604003906250},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint5_angular_918_130897_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-1.77185e-10 -0.0148585 0.0148585 -1.77198e-10 59.2925 33.8585)"><foreignObject x="-1124.44" y="-1124.44" width="2248.89" height="2248.89"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="60" cy="34" r="15" transform="rotate(90 60 34)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-3.5437065548649116e-07,&#34;m01&#34;:29.716979980468750,&#34;m02&#34;:44.433963775634766,&#34;m10&#34;:-29.716979980468750,&#34;m11&#34;:-3.5439549606053333e-07,&#34;m12&#34;:48.716979980468750},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<defs>
<clipPath id="paint0_angular_918_130897_clip_path"><circle cx="15" cy="15" r="15" transform="matrix(-1 0 0 1 45 71)"/></clipPath><clipPath id="paint1_angular_918_130897_clip_path"><circle cx="15" cy="15" r="15" transform="matrix(-1 0 0 1 105 71)"/></clipPath><clipPath id="paint2_angular_918_130897_clip_path"><circle cx="60" cy="86" r="15"/></clipPath><clipPath id="paint3_angular_918_130897_clip_path"><circle cx="75" cy="60" r="15" transform="rotate(-180 75 60)"/></clipPath><clipPath id="paint4_angular_918_130897_clip_path"><circle cx="45" cy="60" r="15"/></clipPath><clipPath id="paint5_angular_918_130897_clip_path"><circle cx="60" cy="34" r="15" transform="rotate(90 60 34)"/></clipPath></defs>
</svg>
