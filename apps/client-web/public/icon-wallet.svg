<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_918_130892" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="120" height="120">
<rect width="120" height="120" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_918_130892)">
<g clip-path="url(#paint0_angular_918_130892_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-0.0696875 0 -0.000891162 -0.10839 92.1875 39.1875)"><foreignObject x="-1020.26" y="-1020.26" width="2040.51" height="2040.51"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M22.5 32H110V88.25H22.5V32Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-139.3750,&#34;m01&#34;:-1.7823247909545898,&#34;m02&#34;:162.76615905761719,&#34;m10&#34;:1.6859036952934781e-12,&#34;m11&#34;:-216.78057861328125,&#34;m12&#34;:147.57778930664062},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint1_angular_918_130892_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.0104098 0 0 0.0116949 99.5902 54.7653)"><foreignObject x="-1096.06" y="-1096.06" width="2192.13" height="2192.13"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M89.375 48.875H110V60.125H89.375V48.875Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:20.819576263427734,&#34;m01&#34;:4.3256522495871419e-12,&#34;m02&#34;:89.180427551269531,&#34;m10&#34;:-1.8649158281015821e-13,&#34;m11&#34;:23.389810562133789,&#34;m12&#34;:43.070423126220703},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint2_angular_918_130892_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.000141509 0.0157075 0.0157075 -0.000141509 22.1415 73.7075)"><foreignObject x="-1072.83" y="-1072.83" width="2145.67" height="2145.67"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="15" cy="15" r="15" transform="matrix(-1 0 0 1 37 58)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:0.28301796317100525,&#34;m01&#34;:31.415094375610352,&#34;m02&#34;:6.2924532890319824,&#34;m10&#34;:31.415094375610352,&#34;m11&#34;:-0.28301796317100525,&#34;m12&#34;:58.141510009765625},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g clip-path="url(#paint3_angular_918_130892_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-0.0148585 0 0 -0.0148585 51.8585 73.7075)"><foreignObject x="-1124.44" y="-1124.44" width="2248.89" height="2248.89"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(247, 107, 21, 1) 0deg,rgba(255, 255, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><circle cx="52" cy="73" r="15" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.96862745285034180,&#34;g&#34;:0.41960784792900085,&#34;b&#34;:0.082352943718433380,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-29.716979980468750,&#34;m01&#34;:-2.4054962882713582e-11,&#34;m02&#34;:66.716979980468750,&#34;m10&#34;:-7.8659621134333224e-13,&#34;m11&#34;:-29.716979980468750,&#34;m12&#34;:88.56604003906250},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<defs>
<clipPath id="paint0_angular_918_130892_clip_path"><path d="M22.5 32H110V88.25H22.5V32Z"/></clipPath><clipPath id="paint1_angular_918_130892_clip_path"><path d="M89.375 48.875H110V60.125H89.375V48.875Z"/></clipPath><clipPath id="paint2_angular_918_130892_clip_path"><circle cx="15" cy="15" r="15" transform="matrix(-1 0 0 1 37 58)"/></clipPath><clipPath id="paint3_angular_918_130892_clip_path"><circle cx="52" cy="73" r="15"/></clipPath></defs>
</svg>
