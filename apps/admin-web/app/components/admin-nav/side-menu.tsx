"use client";
import { HamburgerMenuIcon } from "@radix-ui/react-icons";
import { Button, IconButton } from "@radix-ui/themes";
import clsx from "clsx";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Fragment, ReactNode, useState } from "react";

export interface SideMenuConfig {
  href: string;
  label: string;
  children?: SideMenuConfig[];
}

const LinkButton = ({
  href,
  label,
  indent,
  toggleMenu,
}: {
  href: string;
  label: string;
  indent: number;
  toggleMenu: () => void;
}) => {
  const pathname = usePathname();

  const active = pathname === href;

  return (
    <Link href={href}>
      <Button
        asChild
        color="gray"
        highContrast
        variant={active ? "soft" : "outline"}
        onClick={toggleMenu}
        style={{
          boxShadow: "none",
          width: `calc(100% - ${indent * 20}px)`,
          marginLeft: `${indent * 20}px`,
        }}
        className="w-full"
      >
        <div>
          <span className="mr-auto">{label}</span>
        </div>
      </Button>
    </Link>
  );
};

const renderMenuItems = (
  config: SideMenuConfig[],
  toggleMenu: () => void,
  indent = 0,
) => {
  return config.map(({ href, label, children }): ReactNode => {
    return (
      <Fragment key={href}>
        <LinkButton
          href={href}
          label={label}
          indent={indent}
          toggleMenu={toggleMenu}
        />
        {children && renderMenuItems(children, toggleMenu, indent + 1)}
      </Fragment>
    );
  });
};

export const SideMenu = ({ config }: { config: SideMenuConfig[] }) => {
  const [visible, setVisible] = useState(false);

  const toggleMenu = () => setVisible((prev) => !prev);

  return (
    <>
      <div className="lg:hidden">
        <IconButton
          radius="full"
          color="gray"
          variant="soft"
          onClick={toggleMenu}
        >
          <HamburgerMenuIcon />
        </IconButton>
      </div>

      <div
        className={clsx(
          "flex overflow-hidden",
          "fixed z-10 left-0 top-0 w-full h-full bg-black/50 opacity-0 transition-all",
          {
            "opacity-100 pointer-events-": visible,
            "pointer-events-none": !visible,
          },
        )}
        onClick={() => setVisible(false)}
      >
        <div
          className={clsx(
            "bg-white fixed z-10 top-0 left-0 w-45 h-full p-4 flex flex-col gap-2",
            "transition-transform",
            {
              "translate-x-0": visible,
              "-translate-x-full": !visible,
            },
          )}
          onClickCapture={(e) => e.stopPropagation()}
        >
          {renderMenuItems(config, toggleMenu)}
        </div>
      </div>
    </>
  );
};
