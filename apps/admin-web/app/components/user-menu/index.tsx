"use client";
import service from "@/api";
import { API_BASE_URL } from "@/app/constants";
import { CaretDownIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import { NavMenu } from "@repo/ui/nav-menu";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";

export const UserMenu = () => {
  const { data } = useQuery({
    queryFn: async () => {
      const res = await service.getOperatorProfile();
      return res.data?.data;
    },
    queryKey: ["operator-profile"],
  });

  const isAuthed = data?.id !== undefined;
  const { name } = data ?? {};

  if (!isAuthed) return null;

  const config = [
    {
      item: <Link href="/settings">Settings</Link>,
      label: "Settings",
    },
    {
      item: (
        <button
          onClick={() => {
            window.location.href = `${API_BASE_URL}/api/v1/auth/logout`;
          }}
        >
          Logout
        </button>
      ),
      label: "Logout",
    },
  ];

  return (
    <div className="flex gap-2 items-center">
      <NavMenu linkConfig={config}>
        <Button
          radius="full"
          variant="soft"
          color="gray"
          style={{ background: "none" }}
        >
          <div className="flex gap-2 items-center">
            <span>{name}</span>
            <CaretDownIcon />
          </div>
        </Button>
      </NavMenu>
    </div>
  );
};
