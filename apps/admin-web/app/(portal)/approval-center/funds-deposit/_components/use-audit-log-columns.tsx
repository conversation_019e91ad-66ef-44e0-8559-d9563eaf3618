import { useMemo } from "react";
import { createColumnHelper } from "@tanstack/react-table";
import {
  DepositStatusBadge,
  DepositStatusBadgeProps,
} from "./deposit-status-badge";

interface AuditLogData {
  status?: string;
  reasonDescription?: string;
  operatorName?: string;
  auditTime: string;
}

const columnHelper = createColumnHelper<AuditLogData>();

export const useAuditLogColumns = () => {
  return useMemo(
    () => [
      columnHelper.accessor("status", {
        header: "Status",
        cell: ({ getValue }) => {
          const status = getValue() as DepositStatusBadgeProps["status"];
          return <DepositStatusBadge status={status} />;
        },
        size: 120,
      }),
      columnHelper.accessor("reasonDescription", {
        header: "Audit reason",
        cell: ({ getValue, row }) => {
          const value = getValue();
          const status = row.original.status;
          return value || (status === "APPROVED" ? "-" : "Below minimum funds");
        },
        size: 200,
      }),
      columnHelper.accessor("operatorName", {
        header: "Audit user",
        cell: ({ getValue }) => {
          const value = getValue();
          return value || "{user}";
        },
        size: 150,
      }),
      columnHelper.accessor("auditTime", {
        header: "Audit time",
        cell: ({ getValue }) => {
          const value = getValue();
          return value;
        },
        size: 180,
      }),
    ],
    [],
  );
};
