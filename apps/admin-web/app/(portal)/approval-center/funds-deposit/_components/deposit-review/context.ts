import { createContext } from "react";

export interface DepositSubmission {
  depositId: string;
  action: "APPROVE" | "REJECT";
}

export interface DepositReviewDialogContextType {
  submission: DepositSubmission | null;
  setSubmission: (submission: DepositSubmission | null) => void;
}

export const DepositReviewDialogContext =
  createContext<DepositReviewDialogContextType>({
    submission: null,
    setSubmission: () => {},
  });
