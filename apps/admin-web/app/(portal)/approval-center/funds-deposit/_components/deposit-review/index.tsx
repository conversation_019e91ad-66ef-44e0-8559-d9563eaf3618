import { useState, ReactNode } from "react";
import { DepositReviewDialogContext, DepositSubmission } from "./context";
import { DepositReviewDialog } from "./deposit-review-dialog";

export { DepositReviewDialogContext } from "./context";
export type { DepositSubmission } from "./context";

interface DepositReviewDialogProviderProps {
  children: ReactNode;
}

export const DepositReviewDialogProvider = ({
  children,
}: DepositReviewDialogProviderProps) => {
  const [submission, setSubmission] = useState<DepositSubmission | null>(null);

  return (
    <DepositReviewDialogContext.Provider value={{ submission, setSubmission }}>
      {children}
      <DepositReviewDialog />
    </DepositReviewDialogContext.Provider>
  );
};
