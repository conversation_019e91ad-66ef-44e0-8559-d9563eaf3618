import { use, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Dialog, Button, Flex, Text, Select, TextArea } from "@radix-ui/themes";
import service from "@/api";
import { DepositReviewDialogContext } from "./context";
import type { DepositReviewRequest } from "@/api/data-contracts";
import { Toast } from "@/app/utils/toast";

export const DepositReviewDialog = () => {
  const { submission, setSubmission } = use(DepositReviewDialogContext);
  const [reason, setReason] = useState<DepositReviewRequest["reason"] | "">("");
  const [reasonExtra, setReasonExtra] = useState("");
  const queryClient = useQueryClient();

  const isComplete = Boolean(
    submission?.action === "APPROVE" ||
      (reason === "OTHER_REASON" ? !!reasonExtra : !!reason),
  );

  const reviewMutation = useMutation({
    mutationFn: async (data: DepositReviewRequest) => {
      return await service.reviewDeposit(data);
    },
    onSuccess: () => {
      // Invalidate and refetch deposit queries
      queryClient.invalidateQueries({ queryKey: ["fiat-deposits"] });
      queryClient.invalidateQueries({ queryKey: ["crypto-deposits"] });
      setSubmission(null);
      setReason("");
      setReasonExtra("");
      switch (submission?.action) {
        case "APPROVE":
          Toast.show("User's transaction approved.", "success");
          break;
        case "REJECT":
          Toast.show("User's transaction rejected.", "success");
          break;
      }
    },
  });

  const handleClose = () => {
    setSubmission(null);
    setReason("");
    setReasonExtra("");
  };

  const { data: reasonOptions } = useQuery({
    queryFn: async () => {
      if (submission?.action) {
        const res = await service.getReviewReasons({
          action: submission?.action,
        });
        return res.data.data;
      }
      return [];
    },
    queryKey: ["review-reasons", submission?.action],
  });

  const handleSubmit = () => {
    if (!submission) return;
    const { action, depositId } = submission;

    if (action === "REJECT" && reason) {
      reviewMutation.mutate({
        depositId,
        action,
        reason,
        reasonExtra: reasonExtra || undefined,
      });
    }

    if (action === "APPROVE") {
      reviewMutation.mutate({
        depositId,
        action,
        reason: "APPROVED",
      });
    }
  };

  const renderContent = () => {
    switch (submission?.action) {
      case "APPROVE": {
        return (
          <>
            <Dialog.Title>Confirm approval of transaction?</Dialog.Title>
            <Dialog.Description size="2" mb="4">
              {`Are you sure you want to approve this user's transaction? Once approved, the deposited amount will be added to and calculated as part of the total investment portfolio. Please note that this action is irreversible.`}
            </Dialog.Description>
          </>
        );
      }
      case "REJECT": {
        return (
          <>
            <Dialog.Title>Confirm rejection of transaction?</Dialog.Title>
            <Flex direction="column" gap="3">
              <Flex direction="column" gap="2">
                <Text as="label" size="2" weight="bold">
                  Rejection reason
                </Text>
                <Select.Root
                  value={reason}
                  onValueChange={(value) =>
                    setReason(value as DepositReviewRequest["reason"])
                  }
                >
                  <Select.Trigger placeholder="Select rejection reason" />
                  <Select.Content>
                    {reasonOptions?.map(({ reason = "", text }) => (
                      <Select.Item key={reason} value={reason}>
                        {text}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select.Root>
              </Flex>

              {reason === "OTHER_REASON" && (
                <Flex direction="column" gap="2">
                  <Text as="label" size="2" weight="bold">
                    Enter reason*
                  </Text>
                  <TextArea
                    required
                    value={reasonExtra}
                    onChange={(e) => setReasonExtra(e.target.value)}
                    maxLength={500}
                  />
                </Flex>
              )}
              {reason && (
                <Text size="2">
                  {`This user's deposits will be declined, and any deposited funds
                  must be refunded to the user within 30 working days.`}
                </Text>
              )}
            </Flex>
          </>
        );
      }
      default:
        return null;
    }
  };

  return (
    <Dialog.Root open={!!submission} onOpenChange={handleClose}>
      <Dialog.Content maxWidth="400px">
        {renderContent()}
        <Flex gap="3" mt="4" justify="end">
          <Dialog.Close>
            <Button radius="full" variant="soft" color="gray">
              Cancel
            </Button>
          </Dialog.Close>
          <Button
            disabled={!isComplete}
            radius="full"
            onClick={handleSubmit}
            loading={reviewMutation.isPending}
          >
            {submission?.action === "APPROVE" ? "Approve" : "Reject"}
          </Button>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};
