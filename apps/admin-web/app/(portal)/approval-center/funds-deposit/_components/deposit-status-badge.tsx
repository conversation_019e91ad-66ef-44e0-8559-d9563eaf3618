import { Badge } from "@radix-ui/themes";

export interface DepositStatusBadgeProps {
  status?: "PENDING" | "APPROVED" | "REJECTED";
}

export const DepositStatusBadge = ({ status }: DepositStatusBadgeProps) => {
  if (!status) return null;

  const getStatusConfig = (status: string) => {
    switch (status) {
      case "PENDING":
        return { color: "blue" as const, label: "Pending" };
      case "APPROVED":
        return { color: "green" as const, label: "Approved" };
      case "REJECTED":
        return { color: "red" as const, label: "Rejected" };
      default:
        return { color: "gray" as const, label: status };
    }
  };

  const { color, label } = getStatusConfig(status);

  return (
    <Badge color={color} variant="soft" size="1" radius="full">
      {label}
    </Badge>
  );
};
