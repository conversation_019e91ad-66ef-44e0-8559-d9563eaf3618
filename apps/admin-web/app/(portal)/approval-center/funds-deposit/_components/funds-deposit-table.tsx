import { Table } from "@radix-ui/themes";
import { FiatDepositDetailsResponse } from "@/api/data-contracts";
import { InfoLayout } from "@repo/ui/info-layout";
import { getCommonPinningStyles } from "@repo/ui/utils/table-utils";
import { LoadingPlaceholder } from "@repo/ui/loading-placeholder";

import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

import style from "./index.module.scss";
import { useFundsDepositColumns } from "./use-funds-deposit-columns";
import type { DepositSubmission } from "./deposit-review/context";

// Extended type to include depositType
type DepositRecord = FiatDepositDetailsResponse & {
  depositType: "FIAT" | "CRYPTO";
};

interface FundsDepositTableProps {
  data: DepositRecord[];
  isLoading?: boolean;
  onDepositReview?: (value: DepositSubmission) => void;
  onViewDetails?: (record: DepositRecord) => void;
}

export const FundsDepositTable = ({
  data,
  isLoading = false,
  onDepositReview,
  onViewDetails,
}: FundsDepositTableProps) => {
  const columns = useFundsDepositColumns({ onDepositReview, onViewDetails });

  const table = useReactTable({
    data,
    columns,
    state: { columnPinning: { left: ["userPublicId"], right: ["action"] } },
    getCoreRowModel: getCoreRowModel(),
  });

  if (!data.length) {
    if (isLoading) {
      return <LoadingPlaceholder />;
    }
    return (
      <div className="bg-[#00000008] rounded-lg">
        <InfoLayout
          className="py-10 px-6"
          icon="/empty-file.png"
          iconAlt="no data"
          title="No records found"
          description="Deposit records will appear here"
        />
      </div>
    );
  }

  return (
    <div className={style.tableContainer}>
      <Table.Root
        variant="surface"
        size="3"
        layout="fixed"
        className={style.override}
      >
        <Table.Header>
          {table.getHeaderGroups().map((headerGroup) => (
            <Table.Row key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Table.ColumnHeaderCell
                  key={header.id}
                  style={{
                    ...getCommonPinningStyles(header.column),
                    minWidth: header.getSize(),
                  }}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </Table.ColumnHeaderCell>
              ))}
            </Table.Row>
          ))}
        </Table.Header>
        <Table.Body>
          {table.getRowModel().rows.map((row) => (
            <Table.Row key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <Table.Cell
                  key={cell.id}
                  style={{
                    ...getCommonPinningStyles(cell.column),
                  }}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </Table.Cell>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
    </div>
  );
};
