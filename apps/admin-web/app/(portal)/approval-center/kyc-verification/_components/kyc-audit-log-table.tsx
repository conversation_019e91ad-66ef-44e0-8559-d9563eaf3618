import { Table, Text } from "@radix-ui/themes";
import { KycVerificationSearchResponse } from "@/api/data-contracts";
import { KycStatusBadge } from "./kyc-status-badge";
import { InfoLayout } from "@repo/ui/info-layout";

interface KycAuditLogTableProps {
  data: KycVerificationSearchResponse;
}

const formatDate = (dateString?: string) => {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
};

export const KycAuditLogTable = ({ data }: KycAuditLogTableProps) => {
  const { status, operatorName, lastUpdated, actionReason } = data;

  // Only show audit log if there's a status change (not PENDING)
  const hasAuditRecord = status !== "PENDING" && lastUpdated;

  if (!hasAuditRecord) {
    return (
      <div className="bg-[#00000008] rounded-lg">
        <InfoLayout
          className="py-10 px-6"
          icon="/empty-file.png"
          iconAlt="no data"
          title="No records found"
          description="Audit records will appear here"
        />
      </div>
    );
  }

  const displayReason = actionReason
    ?.replace(/_/g, " ")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase());

  return (
    <Table.Root variant="surface" size="2">
      <Table.Header>
        <Table.Row>
          <Table.ColumnHeaderCell>Status</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Audit reason</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Audit user</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Audit time</Table.ColumnHeaderCell>
        </Table.Row>
      </Table.Header>
      <Table.Body>
        <Table.Row>
          <Table.Cell>
            <KycStatusBadge status={status} />
          </Table.Cell>
          <Table.Cell>
            <Text size="2">{displayReason || "-"}</Text>
          </Table.Cell>
          <Table.Cell>
            <Text size="2">{operatorName || "{user}"}</Text>
          </Table.Cell>
          <Table.Cell>
            <Text size="2">{formatDate(lastUpdated)}</Text>
          </Table.Cell>
        </Table.Row>
      </Table.Body>
    </Table.Root>
  );
};
