import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Button, Select, TextField } from "@radix-ui/themes";
import { useState } from "react";

interface SearchFiltersProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  statusFilter: string;
  onStatusChange: (value: string) => void;
  onSearch: () => void;
  onReset: () => void;
}

export const SearchFilters = ({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusChange,
  onSearch,
  onReset,
}: SearchFiltersProps) => {
  const [value, setValue] = useState(searchQuery);

  const statusOptions = [
    { value: "PENDING", label: "Pending" },
    { value: "APPROVED", label: "Verified" },
    { value: "REJECTED", label: "Rejected" },
    { value: "RESTRICTED", label: "Prohibited" },
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange(value);
    onSearch();
  };

  const handleReset = () => {
    setValue("");
    onSearchChange("");
    onStatusChange("");
    onReset();
  };

  return (
    <form className="flex gap-4 items-end" onSubmit={handleSubmit}>
      <div className="flex-1 max-w-[400px]">
        <TextField.Root
          placeholder="Search for portfolio ID, email address"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          size="2"
        >
          <TextField.Slot side="left">
            <MagnifyingGlassIcon height="16" width="16" />
          </TextField.Slot>
        </TextField.Root>
      </div>

      <div className="min-w-[120px]">
        <Select.Root value={statusFilter} onValueChange={onStatusChange}>
          <Select.Trigger placeholder="KYC status" />
          <Select.Content>
            {statusOptions.map(({ value, label }) => (
              <Select.Item key={value} value={value}>
                <div className="min-w-[80px]">{label}</div>
              </Select.Item>
            ))}
          </Select.Content>
        </Select.Root>
      </div>

      <Button
        type="submit"
        radius="full"
        color="gray"
        highContrast
        variant="soft"
        size="2"
      >
        Search
      </Button>

      <Button
        type="button"
        radius="full"
        color="gray"
        variant="outline"
        size="2"
        onClick={handleReset}
      >
        Reset
      </Button>
    </form>
  );
};
