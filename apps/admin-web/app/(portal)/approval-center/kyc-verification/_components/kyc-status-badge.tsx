import { Badge } from "@radix-ui/themes";

export const KycStatusBadge = ({ status }: { status?: string }) => {
  switch (status) {
    case "APPROVED":
      return (
        <Badge radius="full" color="green">
          Verified
        </Badge>
      );
    case "PENDING":
      return (
        <Badge radius="full" color="blue">
          Pending
        </Badge>
      );
    case "REJECTED":
      return (
        <Badge radius="full" color="red">
          Rejected
        </Badge>
      );
    case "RESTRICTED":
      return (
        <Badge radius="full" color="red">
          Prohibited
        </Badge>
      );
    default:
      return (
        <Badge radius="full" color="gray">
          Not verified
        </Badge>
      );
  }
};
