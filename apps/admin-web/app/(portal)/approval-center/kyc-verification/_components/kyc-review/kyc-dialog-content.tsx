import { useState } from "react";
import { Heading, Select, Text, TextArea } from "@radix-ui/themes";
import { KycSubmission } from "./context";
import { KycOperatorReasonDTO } from "@/api/data-contracts";
import { useMutation } from "@tanstack/react-query";
import service from "@/api";
import { KycDialogActions } from "./kyc-dialog-actions";

interface KycDialogContentProps {
  submission: KycSubmission;
  reasonOptions: KycOperatorReasonDTO[];
  onSuccess: () => void;
}

export const KycDialogContent = ({
  submission,
  reasonOptions,
  onSuccess,
}: KycDialogContentProps) => {
  const { action, submissionId } = submission;

  const [reason, setReason] =
    useState<KycOperatorReasonDTO["reason"]>(undefined);
  const [reasonExtra, setReasonExtra] = useState<string | undefined>();

  const { mutate: reviewKyc, isPending } = useMutation({
    mutationFn: async () => {
      if (submission) {
        await service.reviewKyc({ action, submissionId, reason, reasonExtra });
      }
    },
    onSuccess,
    mutationKey: ["review-kyc"],
  });

  const isComplete = Boolean(
    action === "APPROVE" ||
      (reason === "OTHER_REASON" && reasonExtra) ||
      reason,
  );

  const getConfirmButtonText = () => {
    switch (action) {
      case "APPROVE":
        return "Approve";
      case "REJECT":
        return "Reject";
      case "RESTRICT":
        return "Restrict";
      default:
        return "";
    }
  };

  const renderContent = () => {
    switch (action) {
      case "APPROVE":
        return (
          <Text size="2">{`Are you sure you want to approve this user's KYC verification? Once approved, they will gain access to deposit funds on their portal. This action cannot be undone.`}</Text>
        );
      case "REJECT":
      case "RESTRICT":
        return (
          <>
            <Heading as="h6" size="2" weight="medium">
              {action === "REJECT" ? "Rejection reason" : "Restriction reason"}
            </Heading>
            <Select.Root
              value={reason}
              onValueChange={(value) =>
                setReason(value as KycOperatorReasonDTO["reason"])
              }
            >
              <Select.Trigger placeholder="Reason" />
              <Select.Content>
                {reasonOptions.map(({ reason: optionReason = "", text }) => (
                  <Select.Item key={optionReason} value={optionReason}>
                    {text}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Root>
            {reason === "OTHER_REASON" && (
              <>
                <Heading as="h6" size="2" weight="medium">
                  Enter reason*
                </Heading>
                <TextArea
                  value={reasonExtra}
                  onChange={(e) => setReasonExtra(e.target.value)}
                />
              </>
            )}
            {reason && (
              <Text size="2">
                {reason === "OTHER_REASON"
                  ? `Rejecting this user's KYC application will permanently deny their verification. They will be notified, and their account access will be restricted based on compliance rules.`
                  : `This user's KYC application will be rejected, and they will need to resubmit their verification.`}
              </Text>
            )}
          </>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex flex-col gap-2">{renderContent()}</div>
      <KycDialogActions
        isPending={isPending}
        isComplete={isComplete}
        getConfirmText={getConfirmButtonText}
        onConfirm={() => reviewKyc()}
      />
    </>
  );
};
