import { use } from "react";
import { AlertDialog } from "@radix-ui/themes";
import { KycReviewDialogContext } from "./context";
import { KycDialogContent } from "./kyc-dialog-content";

export const KycReviewDialog = () => {
  const { submission, setSubmission, reasonOptions } = use(
    KycReviewDialogContext,
  );

  const { action } = submission ?? {};

  const getDialogTitle = () => {
    switch (action) {
      case "APPROVE":
        return "Confirm KYC Approval";
      case "REJECT":
        return "Confirm KYC Rejection";
      case "RESTRICT":
        return "Confirm KYC Restriction";
      default:
        return "";
    }
  };

  return (
    <AlertDialog.Root
      open={submission != null}
      onOpenChange={() => {
        setSubmission(null);
      }}
    >
      <AlertDialog.Content maxWidth="400px" size="2">
        <AlertDialog.Title size="5" mb="4">
          {getDialogTitle()}
        </AlertDialog.Title>
        {submission && (
          <KycDialogContent
            submission={submission}
            reasonOptions={reasonOptions}
            onSuccess={() => {
              setSubmission(null);
            }}
          />
        )}
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
};
