import type { KycOperatorReasonDTO } from "@/api/data-contracts";
import { createContext } from "react";

export type KycSubmission = {
  submissionId: number;
  action: "APPROVE" | "REJECT" | "RESTRICT";
};

export const KycReviewDialogContext = createContext<{
  submission: KycSubmission | null;
  setSubmission: (submission: KycSubmission | null) => void;
  reasonOptions: KycOperatorReasonDTO[];
}>({
  submission: null,
  setSubmission: () => {},
  reasonOptions: [],
});
