import { Al<PERSON>D<PERSON><PERSON>, Button, Flex } from "@radix-ui/themes";

interface KycDialogActionsProps {
  isPending: boolean;
  isComplete: boolean;
  getConfirmText: () => string;
  onConfirm: () => void;
}

export const KycDialogActions = ({
  isPending,
  isComplete,
  getConfirmText,
  onConfirm,
}: KycDialogActionsProps) => {
  return (
    <Flex gap="3" mt="4" justify="end">
      <AlertDialog.Cancel>
        <Button
          variant="soft"
          radius="full"
          size="2"
          color="gray"
          disabled={isPending}
        >
          Cancel
        </Button>
      </AlertDialog.Cancel>
      <Button
        loading={isPending}
        variant="solid"
        radius="full"
        size="2"
        color="orange"
        disabled={!isComplete}
        onClick={onConfirm}
      >
        {getConfirmText()}
      </Button>
    </Flex>
  );
};