import { PropsWithChildren, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import service from "@/api";
import { KycReviewDialogContext, KycSubmission } from "./context";
import { KycReviewDialog } from "./kyc-review-dialog";

export const KycReviewDialogProvider = ({ children }: PropsWithChildren) => {
  const [submission, setSubmission] = useState<KycSubmission | null>(null);
  const { action } = submission ?? {};

  const { data: reasonOptions = [] } = useQuery({
    queryFn: async () => {
      if (action) {
        const res = await service.getReviewReasons({ action });
        return res.data?.data;
      }
      return [];
    },
    queryKey: ["review-reasons", action],
  });

  return (
    <KycReviewDialogContext
      value={{
        submission,
        setSubmission,
        reasonOptions,
      }}
    >
      {children}
      <KycReviewDialog />
    </KycReviewDialogContext>
  );
};
