import { ResponseResultPagedKycVerificationSearchResponse } from "@/api/data-contracts";
import { generateMockKycData, sampleKycData } from "./mock-data";
import { getAllDemoScenarios } from "./demo-scenarios";

// Toggle this to enable/disable mock data
export const USE_MOCK_DATA = false;

export const mockSearchKycVerifications = async (params?: {
  publicId?: string;
  email?: string;
  status?: "PENDING" | "APPROVED" | "REJECTED" | "RESTRICTED";
  page?: number;
  pageSize?: number;
}): Promise<{ data?: ResponseResultPagedKycVerificationSearchResponse }> => {
  // Simulate API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 500 + Math.random() * 1000),
  );

  const page = params?.page || 1;
  const pageSize = params?.pageSize || 20;

  let mockData = generateMockKycData(page, pageSize);

  // Include demo scenarios in the first page for easy testing
  if (page === 1) {
    const demoScenarios = getAllDemoScenarios();
    const generatedRecords = mockData.records || [];

    // Replace first few records with demo scenarios
    const combinedRecords = [
      ...demoScenarios,
      ...generatedRecords.slice(demoScenarios.length),
    ].slice(0, pageSize);

    mockData = {
      ...mockData,
      records: combinedRecords,
    };
  }

  // Apply filters if provided
  if (params?.publicId || params?.email || params?.status) {
    let filteredRecords = mockData.records || [];

    // Filter by portfolio ID
    if (params.publicId) {
      filteredRecords = filteredRecords.filter((record) =>
        record.publicId?.toLowerCase().includes(params.publicId!.toLowerCase()),
      );
    }

    // Filter by email
    if (params.email) {
      filteredRecords = filteredRecords.filter((record) =>
        record.email?.toLowerCase().includes(params.email!.toLowerCase()),
      );
    }

    // Filter by status
    if (params.status) {
      filteredRecords = filteredRecords.filter(
        (record) => record.status === params.status,
      );
    }

    // Recalculate pagination for filtered results
    const totalFilteredItems = filteredRecords.length;
    const totalFilteredPages = Math.ceil(totalFilteredItems / pageSize);
    const startIndex = (page - 1) * pageSize;
    const paginatedRecords = filteredRecords.slice(
      startIndex,
      startIndex + pageSize,
    );

    mockData = {
      records: paginatedRecords,
      totalItems: totalFilteredItems,
      page,
      pageSize,
      totalPages: totalFilteredPages,
    };
  }

  return {
    data: {
      code: "0",
      message: "Success",
      data: mockData,
      requestId: `mock-${Date.now()}`,
    },
  };
};

// Mock data for specific test scenarios
export const getMockDataForTesting = () => {
  return {
    data: {
      code: "0",
      message: "Success",
      data: {
        records: sampleKycData,
        totalItems: sampleKycData.length,
        page: 1,
        pageSize: 20,
        totalPages: 1,
      },
      requestId: `test-${Date.now()}`,
    },
  };
};
