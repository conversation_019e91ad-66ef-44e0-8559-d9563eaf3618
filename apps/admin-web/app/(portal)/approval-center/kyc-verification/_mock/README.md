# KYC Verification Mock Data

This directory contains mock data and services for testing the KYC verification page without requiring a real backend API.

## Files

- **`mock-data.ts`** - Contains mock data generators and sample data
- **`mock-service.ts`** - Mock service that simulates the API behavior
- **`README.md`** - This documentation file

## Usage

### Enable/Disable Mock Data

To toggle between mock data and real API calls, edit the `USE_MOCK_DATA` constant in `mock-service.ts`:

```typescript
// Enable mock data
export const USE_MOCK_DATA = true;

// Disable mock data (use real API)
export const USE_MOCK_DATA = false;
```

### Mock Data Features

The mock service provides:

- **157 total records** with realistic pagination
- **All KYC statuses**: PENDING, APPROVED, REJECTED, RESTRICTED
- **Search functionality**: Filter by portfolio ID or email
- **Status filtering**: Filter by KYC status
- **Realistic data**: Names, emails, countries, document types
- **API simulation**: Includes random delays (500-1500ms)

### Sample Data

The mock includes pre-defined sample records for consistent testing:

1. **<PERSON>** - APPROVED (American, Passport)
2. **<PERSON>** - PENDING (Canadian, Driver's License)
3. **Michael Brown** - REJECTED (British, National ID, Document Unreadable)
4. **Sarah Wilson** - RESTRICTED (Australian, Passport, Sanctions Match)
5. **David Garcia** - PENDING (Spanish, National ID)

### Testing Scenarios

You can test various scenarios:

#### Search Tests
- Search for "john" → Should find John Smith
- Search for "gmail.com" → Should find records with Gmail addresses
- Search for "A1B2C3D4" → Should find John Smith's portfolio

#### Filter Tests
- Filter by "PENDING" → Should show pending verifications
- Filter by "REJECTED" → Should show rejected verifications
- Combine search + filter → Should work together

#### Pagination Tests
- Navigate through pages → Should show different records
- Change page size → Should adjust records per page

### Data Structure

Each mock record includes:

```typescript
{
  publicId: string;           // Portfolio ID (e.g., "A1B2C3D4")
  email: string;              // User email
  firstName: string;          // First name
  lastName: string;           // Last name
  nationality: string;        // User nationality
  countryOfResidence: string; // Country of residence
  identityDocumentType: "NATIONAL_ID" | "DRIVERS_LICENSE" | "PASSPORT";
  documentFrontKey: string;   // Document front image key
  documentBackKey?: string;   // Document back image key (optional)
  selfieKey: string;          // Selfie image key
  submissionId: number;       // Unique submission ID
  status: "PENDING" | "APPROVED" | "REJECTED" | "RESTRICTED";
  submissionDate: string;     // ISO date string
  lastUpdated: string;        // ISO date string
  operatorName?: string;      // Operator who processed (if not pending)
  actionReason?: string;      // Reason for approval/rejection
}
```

### Development Tools

A development tools panel is available in the bottom-right corner (development mode only) that shows:

- Mock data status
- Quick test actions
- Data statistics

### Customization

To add more mock data or modify existing data:

1. **Add new sample records** to `sampleKycData` array in `mock-data.ts`
2. **Modify data generators** in the `generateMockKycRecord()` function
3. **Adjust total record count** in `generateMockKycData()` function
4. **Add new test scenarios** in the mock service

### API Compatibility

The mock service maintains full compatibility with the real API:

- Same request parameters
- Same response structure
- Same error handling patterns
- Same pagination behavior

This ensures seamless switching between mock and real data without code changes.
