import {
  KycVerificationSearchResponse,
  PagedKycVerificationSearchResponse,
} from "@/api/data-contracts";

// Mock data generator for KYC verifications
const firstNames = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];

const lastNames = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "Wilson",
  "Anderson",
  "Thomas",
  "Taylor",
  "Moore",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];

const countries = [
  "United States",
  "Canada",
  "United Kingdom",
  "Germany",
  "France",
  "Australia",
  "Japan",
  "South Korea",
  "Singapore",
  "Netherlands",
  "Sweden",
  "Norway",
  "Switzerland",
  "Austria",
  "Belgium",
  "Denmark",
  "Finland",
  "Ireland",
  "New Zealand",
  "Luxembourg",
  "Italy",
  "Spain",
  "Portugal",
  "Czech Republic",
];

const nationalities = [
  "American",
  "Canadian",
  "British",
  "German",
  "French",
  "Australian",
  "Japanese",
  "Korean",
  "Singaporean",
  "Dutch",
  "Swedish",
  "Norwegian",
  "Swiss",
  "Austrian",
  "Belgian",
  "Danish",
  "Finnish",
  "Irish",
  "New Zealand",
  "Luxembourgish",
  "Italian",
  "Spanish",
  "Portuguese",
  "Czech",
];

const statuses: Array<"PENDING" | "APPROVED" | "REJECTED" | "RESTRICTED"> = [
  "PENDING",
  "APPROVED",
  "REJECTED",
  "RESTRICTED",
];

const documentTypes: Array<"NATIONAL_ID" | "DRIVERS_LICENSE" | "PASSPORT"> = [
  "NATIONAL_ID",
  "DRIVERS_LICENSE",
  "PASSPORT",
];

const rejectionReasons = [
  "INVALID_DOCUMENT_TYPE",
  "DOCUMENT_EXPIRED",
  "DOCUMENT_UNREADABLE_BLURRY",
  "DOCUMENT_DAMAGED_OR_TAMPERED",
  "SELFIE_MISMATCH",
  "INCONSISTENT_PERSONAL_DETAILS",
  "SUSPICIOUS_ACTIVITY",
  "SANCTIONS_LIST_MATCH",
  "OTHER_REASON",
];

const operatorNames = [
  "Alice Johnson",
  "Bob Smith",
  "Carol Williams",
  "David Brown",
  "Eva Davis",
  "Frank Miller",
  "Grace Wilson",
  "Henry Moore",
  "Iris Taylor",
  "Jack Anderson",
];

function generateRandomDate(daysAgo: number = 30): string {
  const now = new Date();
  const randomDays = Math.floor(Math.random() * daysAgo);
  const date = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000);
  return date.toISOString();
}

function generatePortfolioId(): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

function generateEmail(firstName: string, lastName: string): string {
  const domains = [
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
    "icloud.com",
  ];
  const domain = domains[Math.floor(Math.random() * domains.length)];
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`;
}

function generateMockKycRecord(): KycVerificationSearchResponse {
  const firstName =
    firstNames[Math.floor(Math.random() * firstNames.length)] ?? "";
  const lastName =
    lastNames[Math.floor(Math.random() * lastNames.length)] ?? "";
  const status = statuses[Math.floor(Math.random() * statuses.length)];
  const submissionDate = generateRandomDate(60);
  const lastUpdated = new Date(
    new Date(submissionDate).getTime() +
      Math.random() * 7 * 24 * 60 * 60 * 1000,
  ).toISOString();

  const identityDocumentType =
    documentTypes[Math.floor(Math.random() * documentTypes.length)];

  const record: KycVerificationSearchResponse = {
    publicId: generatePortfolioId(),
    email: generateEmail(firstName, lastName),
    firstName,
    lastName,
    nationality:
      nationalities[Math.floor(Math.random() * nationalities.length)],
    countryOfResidence: countries[Math.floor(Math.random() * countries.length)],
    identityDocumentType,
    documentFrontKey: `documents/${generatePortfolioId()}/front.jpg`,
    documentBackKey:
      identityDocumentType === "PASSPORT"
        ? `documents/${generatePortfolioId()}/back.jpg`
        : undefined,
    selfieKey: `documents/${generatePortfolioId()}/selfie.jpg`,
    submissionId: Math.floor(Math.random() * 100000) + 1000,
    status,
    submissionDate,
    lastUpdated,
  };

  // Add operator and rejection reason for processed records
  if (status !== "PENDING") {
    record.operatorName =
      operatorNames[Math.floor(Math.random() * operatorNames.length)];

    if (status === "REJECTED" || status === "RESTRICTED") {
      record.actionReason = rejectionReasons[
        Math.floor(Math.random() * rejectionReasons.length)
      ] as any;
    } else if (status === "APPROVED") {
      record.actionReason = "APPROVED";
    }
  }

  return record;
}

export function generateMockKycData(
  page: number = 1,
  pageSize: number = 20,
): PagedKycVerificationSearchResponse {
  const totalItems = 157; // Mock total
  const totalPages = Math.ceil(totalItems / pageSize);

  // Generate records for the current page
  const records: KycVerificationSearchResponse[] = [];
  const startIndex = (page - 1) * pageSize;

  for (let i = 0; i < pageSize && startIndex + i < totalItems; i++) {
    records.push(generateMockKycRecord());
  }

  return {
    records,
    totalItems,
    page,
    pageSize,
    totalPages,
  };
}

// Pre-generated sample data for consistent testing
export const sampleKycData: KycVerificationSearchResponse[] = [
  {
    publicId: "A1B2C3D4",
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Smith",
    nationality: "American",
    countryOfResidence: "United States",
    identityDocumentType: "PASSPORT",
    documentFrontKey: "documents/A1B2C3D4/front.jpg",
    documentBackKey: "documents/A1B2C3D4/back.jpg",
    selfieKey: "documents/A1B2C3D4/selfie.jpg",
    submissionId: 12345,
    status: "APPROVED",
    submissionDate: "2024-01-15T10:30:00Z",
    lastUpdated: "2024-01-16T14:20:00Z",
    operatorName: "Alice Johnson",
    actionReason: "APPROVED",
  },
  {
    publicId: "E5F6G7H8",
    email: "<EMAIL>",
    firstName: "Jane",
    lastName: "Doe",
    nationality: "Canadian",
    countryOfResidence: "Canada",
    identityDocumentType: "DRIVERS_LICENSE",
    documentFrontKey: "documents/E5F6G7H8/front.jpg",
    documentBackKey: "documents/E5F6G7H8/back.jpg",
    selfieKey: "documents/E5F6G7H8/selfie.jpg",
    submissionId: 12346,
    status: "PENDING",
    submissionDate: "2024-01-20T09:15:00Z",
    lastUpdated: "2024-01-20T09:15:00Z",
  },
  {
    publicId: "I9J0K1L2",
    email: "<EMAIL>",
    firstName: "Michael",
    lastName: "Brown",
    nationality: "British",
    countryOfResidence: "United Kingdom",
    identityDocumentType: "NATIONAL_ID",
    documentFrontKey: "documents/I9J0K1L2/front.jpg",
    selfieKey: "documents/I9J0K1L2/selfie.jpg",
    submissionId: 12347,
    status: "REJECTED",
    submissionDate: "2024-01-18T16:45:00Z",
    lastUpdated: "2024-01-19T11:30:00Z",
    operatorName: "Bob Smith",
    actionReason: "DOCUMENT_UNREADABLE_BLURRY",
  },
  {
    publicId: "M3N4O5P6",
    email: "<EMAIL>",
    firstName: "Sarah",
    lastName: "Wilson",
    nationality: "Australian",
    countryOfResidence: "Australia",
    identityDocumentType: "PASSPORT",
    documentFrontKey: "documents/M3N4O5P6/front.jpg",
    documentBackKey: "documents/M3N4O5P6/back.jpg",
    selfieKey: "documents/M3N4O5P6/selfie.jpg",
    submissionId: 12348,
    status: "RESTRICTED",
    submissionDate: "2024-01-17T13:20:00Z",
    lastUpdated: "2024-01-18T08:45:00Z",
    operatorName: "Carol Williams",
    actionReason: "SANCTIONS_LIST_MATCH",
  },
  {
    publicId: "Q7R8S9T0",
    email: "<EMAIL>",
    firstName: "David",
    lastName: "Garcia",
    nationality: "Spanish",
    countryOfResidence: "Spain",
    identityDocumentType: "NATIONAL_ID",
    documentFrontKey: "documents/Q7R8S9T0/front.jpg",
    selfieKey: "documents/Q7R8S9T0/selfie.jpg",
    submissionId: 12349,
    status: "PENDING",
    submissionDate: "2024-01-22T11:00:00Z",
    lastUpdated: "2024-01-22T11:00:00Z",
  },
];
