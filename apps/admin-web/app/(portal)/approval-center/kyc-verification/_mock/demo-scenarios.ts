import { KycVerificationSearchResponse } from "@/api/data-contracts";

// Demo scenarios for testing different KYC states and edge cases
export const demoScenarios = {
  // Recently submitted KYC (pending)
  recentSubmission: {
    publicId: "NEW12345",
    email: "<EMAIL>",
    firstName: "<PERSON>",
    lastName: "New",
    nationality: "American",
    countryOfResidence: "United States",
    identityDocumentType: "PASSPORT" as const,
    documentFrontKey: "documents/NEW12345/front.jpg",
    documentBackKey: "documents/NEW12345/back.jpg",
    selfieKey: "documents/NEW12345/selfie.jpg",
    submissionId: 99001,
    status: "PENDING" as const,
    submissionDate: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
  },

  // Approved after review
  approvedAfterReview: {
    publicId: "APP54321",
    email: "<EMAIL>",
    firstName: "<PERSON>",
    lastName: "Approved",
    nationality: "Canadian",
    countryOfResidence: "Canada",
    identityDocumentType: "DRIVERS_LICENSE" as const,
    documentFrontKey: "documents/APP54321/front.jpg",
    documentBackKey: "documents/APP54321/back.jpg",
    selfieKey: "documents/APP54321/selfie.jpg",
    submissionId: 99002,
    status: "APPROVED" as const,
    submissionDate: new Date(
      Date.now() - 3 * 24 * 60 * 60 * 1000,
    ).toISOString(), // 3 days ago
    lastUpdated: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
    operatorName: "Alice Johnson",
    actionReason: "APPROVED",
  },

  // Rejected for blurry document
  rejectedBlurryDoc: {
    publicId: "REJ98765",
    email: "<EMAIL>",
    firstName: "Charlie",
    lastName: "Rejected",
    nationality: "British",
    countryOfResidence: "United Kingdom",
    identityDocumentType: "NATIONAL_ID" as const,
    documentFrontKey: "documents/REJ98765/front.jpg",
    selfieKey: "documents/REJ98765/selfie.jpg",
    submissionId: 99003,
    status: "REJECTED" as const,
    submissionDate: new Date(
      Date.now() - 5 * 24 * 60 * 60 * 1000,
    ).toISOString(), // 5 days ago
    lastUpdated: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
    operatorName: "Bob Smith",
    actionReason: "DOCUMENT_UNREADABLE_BLURRY",
  },

  // Restricted due to sanctions
  restrictedSanctions: {
    publicId: "RES11111",
    email: "<EMAIL>",
    firstName: "Diana",
    lastName: "Restricted",
    nationality: "German",
    countryOfResidence: "Germany",
    identityDocumentType: "PASSPORT" as const,
    documentFrontKey: "documents/RES11111/front.jpg",
    documentBackKey: "documents/RES11111/back.jpg",
    selfieKey: "documents/RES11111/selfie.jpg",
    submissionId: 99004,
    status: "RESTRICTED" as const,
    submissionDate: new Date(
      Date.now() - 7 * 24 * 60 * 60 * 1000,
    ).toISOString(), // 7 days ago
    lastUpdated: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(), // 6 days ago
    operatorName: "Carol Williams",
    actionReason: "SANCTIONS_LIST_MATCH",
  },

  // Expired document
  expiredDocument: {
    publicId: "EXP22222",
    email: "<EMAIL>",
    firstName: "Eve",
    lastName: "Expired",
    nationality: "French",
    countryOfResidence: "France",
    identityDocumentType: "DRIVERS_LICENSE" as const,
    documentFrontKey: "documents/EXP22222/front.jpg",
    documentBackKey: "documents/EXP22222/back.jpg",
    selfieKey: "documents/EXP22222/selfie.jpg",
    submissionId: 99005,
    status: "REJECTED" as const,
    submissionDate: new Date(
      Date.now() - 10 * 24 * 60 * 60 * 1000,
    ).toISOString(), // 10 days ago
    lastUpdated: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000).toISOString(), // 9 days ago
    operatorName: "David Brown",
    actionReason: "DOCUMENT_EXPIRED",
  },

  // Selfie mismatch
  selfieMismatch: {
    publicId: "MIS33333",
    email: "<EMAIL>",
    firstName: "Frank",
    lastName: "Mismatch",
    nationality: "Australian",
    countryOfResidence: "Australia",
    identityDocumentType: "PASSPORT" as const,
    documentFrontKey: "documents/MIS33333/front.jpg",
    documentBackKey: "documents/MIS33333/back.jpg",
    selfieKey: "documents/MIS33333/selfie.jpg",
    submissionId: 99006,
    status: "REJECTED" as const,
    submissionDate: new Date(
      Date.now() - 12 * 24 * 60 * 60 * 1000,
    ).toISOString(), // 12 days ago
    lastUpdated: new Date(Date.now() - 11 * 24 * 60 * 60 * 1000).toISOString(), // 11 days ago
    operatorName: "Eva Davis",
    actionReason: "SELFIE_MISMATCH",
  },

  // Long pending (needs attention)
  longPending: {
    publicId: "OLD44444",
    email: "<EMAIL>",
    firstName: "Grace",
    lastName: "Old",
    nationality: "Japanese",
    countryOfResidence: "Japan",
    identityDocumentType: "NATIONAL_ID" as const,
    documentFrontKey: "documents/OLD44444/front.jpg",
    selfieKey: "documents/OLD44444/selfie.jpg",
    submissionId: 99007,
    status: "PENDING" as const,
    submissionDate: new Date(
      Date.now() - 15 * 24 * 60 * 60 * 1000,
    ).toISOString(), // 15 days ago
    lastUpdated: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
  },
} as const;

// Function to get all demo scenarios as an array
export const getAllDemoScenarios = (): KycVerificationSearchResponse[] => {
  return Object.values(demoScenarios);
};

// Function to get scenarios by status
export const getScenariosByStatus = (
  status: "PENDING" | "APPROVED" | "REJECTED" | "RESTRICTED",
) => {
  return getAllDemoScenarios().filter((scenario) => scenario.status === status);
};

// Function to get scenarios that need attention (long pending, rejected, restricted)
export const getScenariosNeedingAttention = () => {
  const scenarios = getAllDemoScenarios();
  const now = Date.now();
  const threeDaysAgo = now - 3 * 24 * 60 * 60 * 1000;

  return scenarios.filter((scenario) => {
    // Long pending submissions (more than 3 days)
    if (scenario.status === "PENDING") {
      const submissionTime = new Date(scenario.submissionDate!).getTime();
      return submissionTime < threeDaysAgo;
    }

    // All rejected and restricted cases
    return scenario.status === "REJECTED" || scenario.status === "RESTRICTED";
  });
};
