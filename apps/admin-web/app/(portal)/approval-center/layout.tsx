"use client";
import { But<PERSON> } from "@radix-ui/themes";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { PropsWithChildren } from "react";

const Layout = ({ children }: PropsWithChildren) => {
  const pathname = usePathname();

  const config = [
    {
      href: "/approval-center/kyc-verification",
      label: "KYC verification",
    },
    {
      href: "/approval-center/funds-deposit",
      label: "Funds deposit",
    },
  ];

  return (
    <main className="h-full w-full max-w-[1270px] grow grid grid-cols-4 md:grid-cols-12 gap-4 px-6 mx-auto">
      <div className="col-span-2 hidden lg:flex flex-col gap-2 border-r border-gray-200 py-6 pr-4 -mx-2">
        {config.map(({ href, label }) => {
          const active = href === pathname;
          return (
            <Link key={href} href={href} className="font-normal">
              <Button
                asChild
                className="w-full"
                variant={active ? "soft" : "outline"}
                color="gray"
                style={{ boxShadow: "none" }}
                highContrast
              >
                <span>{label}</span>
              </Button>
            </Link>
          );
        })}
      </div>
      <div className="col-span-full lg:col-span-10 py-6 lg:pl-4">
        {children}
      </div>
    </main>
  );
};

export default Layout;
