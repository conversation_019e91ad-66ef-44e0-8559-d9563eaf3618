import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Button, TextField } from "@radix-ui/themes";
import { useState } from "react";

interface SearchFiltersProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
}

export const SearchFilters = ({
  searchQuery,
  onSearchChange,
}: SearchFiltersProps) => {
  const [value, setValue] = useState(searchQuery);

  return (
    <form
      className="flex flex gap-4 md:max-w-[400px]"
      onSubmit={(e) => {
        e.preventDefault();
        onSearchChange(value);
      }}
    >
      <TextField.Root
        className="flex-1"
        placeholder="Search for portfolio ID, email address"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        size="2"
      >
        <TextField.Slot side="left">
          <MagnifyingGlassIcon height="16" width="16" />
        </TextField.Slot>
      </TextField.Root>

      <Button radius="full" color="gray" highContrast variant="soft" size="2">
        Search
      </Button>
    </form>
  );
};
