"use client";
import service from "@/api";
import { Heading } from "@radix-ui/themes";
import { Container } from "@repo/ui/layout/container";
import { ProfileListItem } from "@repo/ui/profile-list-item";
import { useQuery } from "@tanstack/react-query";

export default function ProfileOverviewPage() {
  const { data } = useQuery({
    queryFn: async () => {
      const res = await service.getOperatorProfile();
      return res.data?.data;
    },
    queryKey: ["operator-profile"],
  });

  const { name, email } = data ?? {};

  return (
    <main className="grow py-6 md:py-14">
      <Container>
        <div className="col-span-full md:col-span-8 md:col-start-3 flex flex-col">
          <div className="mb-8">
            <Heading as="h3" size="7" weight="bold">
              {name}
            </Heading>
          </div>

          <ProfileListItem label="Email" value={email} />

          <ProfileListItem label="Role" value={"Admin"} />

          {/* <ProfileListItem
            label="Password"
            value={
              <Button size="1" radius="full" color="gray" variant="soft">
                Change password
              </Button>
            }
          /> */}
        </div>
      </Container>
    </main>
  );
}
